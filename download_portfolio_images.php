<?php
/**
 * Portfolio Image Downloader Script
 * 
 * This script reads a CSV file containing portfolio data, extracts image URLs,
 * and downloads the images to a local directory.
 * 
 * <AUTHOR> Agent
 * @date <?php echo date('Y-m-d'); ?>
 */

// Define file paths
$csvFile = 'public/portfolio_living_processed.csv';
$outputDir = 'public/portfolios/';

// Statistics
$stats = [
    'total_images' => 0,
    'downloaded' => 0,
    'failed' => 0,
    'skipped' => 0,
];

/**
 * Download an image from a URL and save it to a local file
 * 
 * @param string $url The URL of the image to download
 * @param string $outputPath The path where the image should be saved
 * @return bool True if the download was successful, false otherwise
 */
function downloadImage($url, $outputPath) {
    // Check if the URL is valid
    if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
        echo "  - Invalid URL: $url\n";
        return false;
    }
    
    // Check if the file already exists
    if (file_exists($outputPath)) {
        echo "  - File already exists: $outputPath (skipping)\n";
        return 'skipped';
    }
    
    try {
        // Try to download the image using file_get_contents
        $imageData = @file_get_contents($url);
        
        if ($imageData === false) {
            // If file_get_contents fails, try using cURL
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $imageData = curl_exec($ch);
            
            if (curl_errno($ch)) {
                echo "  - cURL error: " . curl_error($ch) . " for URL: $url\n";
                curl_close($ch);
                return false;
            }
            
            curl_close($ch);
        }
        
        // Save the image to the output path
        if (file_put_contents($outputPath, $imageData) === false) {
            echo "  - Failed to save image to: $outputPath\n";
            return false;
        }
        
        echo "  - Downloaded: $outputPath\n";
        return true;
    } catch (Exception $e) {
        echo "  - Error downloading image: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Extract the filename from a URL
 * 
 * @param string $url The URL to extract the filename from
 * @return string The filename
 */
function getFilenameFromUrl($url) {
    return basename(parse_url($url, PHP_URL_PATH));
}

try {
    echo "Starting portfolio image download process...\n";
    
    // Check if the CSV file exists
    if (!file_exists($csvFile)) {
        throw new Exception("CSV file not found: $csvFile");
    }
    
    // Check if the CSV file is readable
    if (!is_readable($csvFile)) {
        throw new Exception("Cannot read CSV file: $csvFile");
    }
    
    // Create the output directory if it doesn't exist
    if (!file_exists($outputDir)) {
        echo "Creating output directory: $outputDir\n";
        if (!mkdir($outputDir, 0755, true)) {
            throw new Exception("Failed to create output directory: $outputDir");
        }
    }
    
    // Check if the output directory is writable
    if (!is_writable($outputDir)) {
        throw new Exception("Output directory is not writable: $outputDir");
    }
    
    // Open the CSV file
    $handle = fopen($csvFile, 'r');
    if ($handle === false) {
        throw new Exception("Failed to open CSV file: $csvFile");
    }
    
    // Read the header row to get column indices
    $header = fgetcsv($handle);
    if ($header === false) {
        throw new Exception("Failed to read header from CSV file");
    }
    
    // Find the indices of the columns we need
    $featuredImageIndex = array_search('featured_image-src', $header);
    $otherImagesIndex = array_search('other_images', $header);
    
    if ($featuredImageIndex === false) {
        throw new Exception("Column 'featured_image-src' not found in CSV file");
    }
    
    if ($otherImagesIndex === false) {
        throw new Exception("Column 'other_images' not found in CSV file");
    }
    
    echo "Processing CSV file...\n";
    
    // Process each row in the CSV file
    $rowCount = 0;
    while (($row = fgetcsv($handle)) !== false) {
        $rowCount++;
        echo "Row $rowCount:\n";
        
        // Get the featured image URL
        $featuredImageUrl = isset($row[$featuredImageIndex]) ? trim($row[$featuredImageIndex]) : '';
        
        // Download the featured image if it exists
        if (!empty($featuredImageUrl)) {
            $stats['total_images']++;
            $filename = getFilenameFromUrl($featuredImageUrl);
            $outputPath = $outputDir . $filename;
            
            echo "  Featured image: $featuredImageUrl\n";
            $result = downloadImage($featuredImageUrl, $outputPath);
            
            if ($result === true) {
                $stats['downloaded']++;
            } elseif ($result === 'skipped') {
                $stats['skipped']++;
            } else {
                $stats['failed']++;
            }
        }
        
        // Get the other images URLs
        $otherImagesUrls = isset($row[$otherImagesIndex]) ? trim($row[$otherImagesIndex]) : '';
        
        // Download each of the other images if they exist
        if (!empty($otherImagesUrls)) {
            $urls = explode(',', $otherImagesUrls);
            
            foreach ($urls as $url) {
                $url = trim($url);
                if (!empty($url)) {
                    $stats['total_images']++;
                    $filename = getFilenameFromUrl($url);
                    $outputPath = $outputDir . $filename;
                    
                    echo "  Other image: $url\n";
                    $result = downloadImage($url, $outputPath);
                    
                    if ($result === true) {
                        $stats['downloaded']++;
                    } elseif ($result === 'skipped') {
                        $stats['skipped']++;
                    } else {
                        $stats['failed']++;
                    }
                }
            }
        }
    }
    
    // Close the CSV file
    fclose($handle);
    
    // Display summary
    echo "\nDownload Summary:\n";
    echo "----------------\n";
    echo "Total rows processed: $rowCount\n";
    echo "Total images found: {$stats['total_images']}\n";
    echo "Images downloaded: {$stats['downloaded']}\n";
    echo "Images skipped (already exist): {$stats['skipped']}\n";
    echo "Images failed to download: {$stats['failed']}\n";
    echo "Images saved to: $outputDir\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nProcess completed!\n";
