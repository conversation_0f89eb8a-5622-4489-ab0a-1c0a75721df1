<?php
/**
 * CSV Processor Script
 *
 * This script reads a CSV file, adds a SEO-friendly title column by slugifying the title,
 * extracts image URLs from the images column, extracts filenames from image URLs,
 * and saves the result to a new CSV file.
 *
 * <AUTHOR> Agent
 * @date <?php echo date('Y-m-d'); ?>
 */

// Define file paths
$inputFile = 'public/portfolio_living.csv';
$outputFile = 'public/portfolio_living_processed.csv';

/**
 * Slugify a string
 *
 * Converts a string to a URL-friendly slug by:
 * - Converting to lowercase
 * - Removing special characters
 * - Replacing spaces with hyphens
 *
 * @param string $text The text to slugify
 * @return string The slugified text
 */
function slugify($text) {
    // Convert to lowercase
    $text = strtolower($text);

    // Remove special characters
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);

    // Replace spaces with hyphens
    $text = preg_replace('/\s+/', '-', $text);

    // Remove multiple hyphens
    $text = preg_replace('/-+/', '-', $text);

    // Trim hyphens from beginning and end
    return trim($text, '-');
}

try {
    // Check if input file exists
    if (!file_exists($inputFile)) {
        throw new Exception("Input file not found: $inputFile");
    }

    // Check if input file is readable
    if (!is_readable($inputFile)) {
        throw new Exception("Cannot read input file: $inputFile");
    }

    // Open input file for reading
    $inputHandle = fopen($inputFile, 'r');
    if ($inputHandle === false) {
        throw new Exception("Failed to open input file: $inputFile");
    }

    // Open output file for writing
    $outputHandle = fopen($outputFile, 'w');
    if ($outputHandle === false) {
        fclose($inputHandle);
        throw new Exception("Failed to open output file: $outputFile");
    }

    // Read header row
    $header = fgetcsv($inputHandle);
    if ($header === false) {
        throw new Exception("Failed to read header from input file");
    }

    // Add new columns to header
    $header[] = 'seo_title';
    $header[] = 'other_images';
    $header[] = 'featured_image_name';
    $header[] = 'other_images_names';

    // Write header to output file
    if (fputcsv($outputHandle, $header) === false) {
        throw new Exception("Failed to write header to output file");
    }

    // Process each row
    $rowCount = 0;
    while (($row = fgetcsv($inputHandle)) !== false) {
        $rowCount++;

        // Find the index of the title column
        $titleIndex = array_search('title', $header);
        if ($titleIndex === false) {
            // If 'title' is not found in header, assume it's the second column (index 1)
            $titleIndex = 1;
        }

        // Get the title from the row
        $title = isset($row[$titleIndex]) ? $row[$titleIndex] : '';

        // Generate SEO title
        $seoTitle = slugify($title);

        // Find the index of the images column
        $imagesIndex = array_search('images', $header);
        if ($imagesIndex === false) {
            // If 'images' is not found in header, assume it's the fifth column (index 4)
            $imagesIndex = 4;
        }

        // Get the images JSON from the row
        $imagesJson = isset($row[$imagesIndex]) ? $row[$imagesIndex] : '';

        // Debug information for the first row
        if ($rowCount == 1) {
            echo "Row 1 column count: " . count($row) . "\n";
            echo "Images column index: $imagesIndex\n";
            echo "Images JSON sample: " . substr($imagesJson, 0, 100) . "...\n";
        }

        // Extract image URLs from the JSON
        $imageUrls = [];
        if (!empty($imagesJson)) {
            try {
                $imagesData = json_decode($imagesJson, true);

                // Debug information for the first row
                if ($rowCount == 1) {
                    echo "JSON decode result type: " . gettype($imagesData) . "\n";
                    if (is_array($imagesData)) {
                        echo "Array count: " . count($imagesData) . "\n";
                        if (count($imagesData) > 0) {
                            echo "First item: " . json_encode($imagesData[0]) . "\n";
                        }
                    } else {
                        echo "JSON decode error: " . json_last_error_msg() . "\n";
                    }
                }

                if (is_array($imagesData)) {
                    foreach ($imagesData as $imageData) {
                        if (isset($imageData['images-src']) && !empty($imageData['images-src'])) {
                            $imageUrls[] = $imageData['images-src'];
                        }
                    }
                }
            } catch (Exception $e) {
                // If JSON parsing fails, continue with empty array
                echo "Warning: Failed to parse images JSON in row $rowCount: " . $e->getMessage() . "\n";
            }
        }

        // Join image URLs with commas
        $otherImages = implode(',', $imageUrls);

        // Find the index of the featured image column
        $featuredImageIndex = array_search('featured_image-src', $header);
        if ($featuredImageIndex === false) {
            // If 'featured_image-src' is not found in header, assume it's the third column (index 2)
            $featuredImageIndex = 2;
        }

        // Get the featured image URL from the row
        $featuredImageUrl = isset($row[$featuredImageIndex]) ? $row[$featuredImageIndex] : '';

        // Extract filename from featured image URL
        $featuredImageName = '';
        if (!empty($featuredImageUrl)) {
            $featuredImageName = basename($featuredImageUrl);
        }

        // Extract filenames from other image URLs
        $otherImagesNames = [];
        foreach ($imageUrls as $imageUrl) {
            if (!empty($imageUrl)) {
                $otherImagesNames[] = basename($imageUrl);
            }
        }

        // Join other image filenames with commas
        $otherImagesNamesStr = implode(',', $otherImagesNames);

        // Add new columns to row
        $row[] = $seoTitle;
        $row[] = $otherImages;
        $row[] = $featuredImageName;
        $row[] = $otherImagesNamesStr;

        // Write row to output file
        if (fputcsv($outputHandle, $row) === false) {
            throw new Exception("Failed to write row $rowCount to output file");
        }
    }

    // Close file handles
    fclose($inputHandle);
    fclose($outputHandle);

    // Display success message
    echo "Success! Processed $rowCount rows.\n";
    echo "Original CSV: $inputFile\n";
    echo "Modified CSV with SEO titles, image URLs, and filenames: $outputFile\n";

} catch (Exception $e) {
    // Display error message
    echo "Error: " . $e->getMessage() . "\n";

    // Close file handles if they are open
    if (isset($inputHandle) && is_resource($inputHandle)) {
        fclose($inputHandle);
    }
    if (isset($outputHandle) && is_resource($outputHandle)) {
        fclose($outputHandle);
    }

    // Exit with error code
    exit(1);
}
