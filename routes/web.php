<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MainController;
use App\Http\Controllers\PortfoliosController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\CitiesController;
use App\Http\Controllers\LeadsController;

Route::get('/',[MainController::class,'home']);
Route::get('about-us',[MainController::class,'about']);
Route::get('reviews',[MainController::class,'reviews']);
Route::get('contact-us',[MainController::class,'contactUs']);
Route::get('privacy-policy',[MainController::class,'privacyPolicy']);
Route::get('terms-conditions',[MainController::class,'termsConditions']);
Route::get('get-estimate/{type?}',[MainController::class,'getEstimate']);

Route::get('interior-designers-in-{city?}',[CitiesController::class,'interiorDesignersIn'])->name('interior.cities.detail');

// Frontend services routes
Route::get('services/{cat?}/{seourl?}', [ServicesController::class,'frontendIndex']);

// Portfolios services routes
Route::get('portfolios/{seourl?}', [PortfoliosController::class,'portfolio']);

// Lead form submission route
Route::post('submit-enquiry', [LeadsController::class, 'store'])->name('submit.enquiry');

// Admin routes
Route::prefix('admin')->middleware('auth')->group(function () {
    // Services routes
    Route::resource('service', ServicesController::class);

    // Portfolio routes
    Route::resource('portfolio', PortfoliosController::class);
    Route::delete('portfolio-image/{portfolioId}/{imageName}', [PortfoliosController::class, 'deleteImage'])->name('portfolio.delete-image');

    // Category routes
    Route::resource('category', CategoriesController::class);

    // City routes
    Route::resource('city', CitiesController::class);
});


Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
