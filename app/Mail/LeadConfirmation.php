<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class LeadConfirmation extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The lead data.
     *
     * @var array
     */
    public $leadData;

    /**
     * The form type (estimate or enquiry).
     *
     * @var string
     */
    public $formType;

    /**
     * Create a new message instance.
     *
     * @param array $leadData
     * @param string $formType
     */
    public function __construct(array $leadData, string $formType)
    {
        $this->leadData = $leadData;
        $this->formType = $formType;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->formType === 'estimate'
            ? 'Your Estimate Request Confirmation - ' . config('app.name')
            : 'Your Enquiry Confirmation - ' . config('app.name');

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.lead-confirmation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
