<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Portfolio extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'project_type',
        'category_id',
        'seo_url',
        'featured_image',
        'images',
        'status',
    ];

    /**
     * Get the images for the portfolio (legacy method for backward compatibility).
     *
     * @deprecated Use images_array attribute instead
     * @return array
     */
    public function images()
    {
        // Return a collection that mimics the old relationship
        if (empty($this->images)) {
            return collect([]);
        }

        $imagesArray = $this->images_array;
        $collection = collect();

        foreach ($imagesArray as $index => $imagePath) {
            $collection->push((object)[
                'id' => $index,
                'portfolio_id' => $this->id,
                'image_path' => $imagePath,
                'sort_order' => $index
            ]);
        }

        return $collection;
    }

    /**
     * Get the project type formatted for display in the frontend.
     */
    public function getProjectTypeClassAttribute()
    {
        $types = [
            'rooms' => 'BedRoom',
            'kitchen' => 'ModularKitchen',
            'living' => 'LivingRoom',
            'in-progress' => 'In-Progress',
            'office' => 'Office',
            'commercial' => 'Construction'
        ];

        return $types[$this->project_type] ?? $this->project_type;
    }

    /**
     * Get the images array from the comma-separated string.
     *
     * @return array
     */
    public function getImagesArrayAttribute()
    {
        if (empty($this->images)) {
            return [];
        }

        return explode(',', $this->images);
    }

    /**
     * Add an image to the images array.
     *
     * @param string $imagePath
     * @return void
     */
    public function addImage($imagePath)
    {
        $imagesArray = $this->images_array;
        $imagesArray[] = $imagePath;
        $this->images = implode(',', $imagesArray);
        $this->save();
    }

    /**
     * Remove an image from the images array.
     *
     * @param string $imagePath
     * @return void
     */
    public function removeImage($imagePath)
    {
        $imagesArray = $this->images_array;
        $imagesArray = array_filter($imagesArray, function($image) use ($imagePath) {
            return $image !== $imagePath;
        });
        $this->images = implode(',', $imagesArray);
        $this->save();
    }

    /**
     * Get the category that this portfolio belongs to.
     */
    public function categoryModel()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the project type (backward compatibility accessor).
     *
     * @return string|null
     */
    public function getProjectTypeAttribute()
    {
        // If category_id exists, get from relationship
        if ($this->category_id && $this->relationLoaded('categoryModel')) {
            return $this->categoryModel->name;
        } elseif ($this->category_id) {
            $category = \App\Models\Category::find($this->category_id);
            return $category ? $category->name : null;
        }

        // Fallback to the old project_type column if it exists
        return $this->attributes['project_type'] ?? null;
    }

    /**
     * Set the project type attribute (backward compatibility mutator).
     *
     * @param string|null $value
     */
    public function setProjectTypeAttribute($value)
    {
        // Store in the old column for backward compatibility during migration
        $this->attributes['project_type'] = $value;

        // Also try to set category_id if category exists
        if ($value) {
            $category = \App\Models\Category::where('name', $value)->first();
            if ($category) {
                $this->category_id = $category->id;
            }
        }
    }
}
