<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'display_name',
        'status',
    ];

    /**
     * Get the services that belong to this category.
     */
    public function services()
    {
        return $this->hasMany(Service::class, 'category_id');
    }

    /**
     * Get the portfolios that belong to this category.
     */
    public function portfolios()
    {
        return $this->hasMany(Portfolio::class, 'category_id');
    }

    /**
     * Get active services for this category.
     */
    public function activeServices()
    {
        return $this->services()->where('status', 'active');
    }

    /**
     * Get active portfolios for this category.
     */
    public function activePortfolios()
    {
        return $this->portfolios()->where('status', 'active');
    }

    /**
     * Scope to get only active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
