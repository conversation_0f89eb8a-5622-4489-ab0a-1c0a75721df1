<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'category',
        'category_id',
        'seo_title',
        'seo_url',
        'description',
        'service_read_more',
        'top_banner_image',
        'status',
        'parent_id',
        'menu_order',
        'show_in_menu',
        'is_interior',
        'is_construction',
        'is_other',
    ];

    /**
     * Get the parent service.
     */
    public function parent()
    {
        return $this->belongsTo(Service::class, 'parent_id');
    }

    /**
     * Get the child services.
     */
    public function children()
    {
        return $this->hasMany(Service::class, 'parent_id')->orderBy('menu_order');
    }

    /**
     * Check if service has children.
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Get active children.
     */
    public function activeChildren()
    {
        return $this->children()->where('status', 'active')->where('show_in_menu', true)->orderBy('menu_order');
    }

    /**
     * Get the category that this service belongs to.
     */
    public function categoryModel()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the category name (backward compatibility accessor).
     *
     * @return string|null
     */
    public function getCategoryAttribute()
    {
        // If category_id exists, get from relationship
        if ($this->category_id && $this->relationLoaded('categoryModel')) {
            return $this->categoryModel->name;
        } elseif ($this->category_id) {
            $category = \App\Models\Category::find($this->category_id);
            return $category ? $category->name : null;
        }

        // Fallback to the old category column if it exists
        return $this->attributes['category'] ?? null;
    }

    /**
     * Set the category attribute (backward compatibility mutator).
     *
     * @param string|null $value
     */
    public function setCategoryAttribute($value)
    {
        // Store in the old column for backward compatibility during migration
        $this->attributes['category'] = $value;

        // Also try to set category_id if category exists
        if ($value) {
            $category = \App\Models\Category::where('name', $value)->first();
            if ($category) {
                $this->category_id = $category->id;
            }
        }
    }
}
