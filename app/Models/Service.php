<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'category_id',
        'seo_title',
        'seo_url',
        'description',
        'service_read_more',
        'top_banner_image',
        'status',
        'parent_id',
        'menu_order',
        'show_in_menu',
        'is_interior',
        'is_construction',
        'is_other',
    ];

    /**
     * Get the parent service.
     */
    public function parent()
    {
        return $this->belongsTo(Service::class, 'parent_id');
    }

    /**
     * Get the child services.
     */
    public function children()
    {
        return $this->hasMany(Service::class, 'parent_id')->orderBy('menu_order');
    }

    /**
     * Check if service has children.
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Get active children.
     */
    public function activeChildren()
    {
        return $this->children()->where('status', 'active')->where('show_in_menu', true)->orderBy('menu_order');
    }

    /**
     * Get the category that this service belongs to.
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the category name.
     *
     * @return string|null
     */
    public function getCategoryNameAttribute()
    {
        return $this->category ? $this->category->name : null;
    }

    /**
     * Get the category display name.
     *
     * @return string|null
     */
    public function getCategoryDisplayNameAttribute()
    {
        return $this->category ? $this->category->display_name : null;
    }
}
