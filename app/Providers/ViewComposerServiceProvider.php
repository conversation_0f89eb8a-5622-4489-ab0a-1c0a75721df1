<?php

namespace App\Providers;

use App\Models\City;
use App\Models\Portfolio;
use App\Models\Service;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share portfolio data with the our_portfolio_block view
        View::composer('includes.our_portfolio_block', function ($view) {
            // Only fetch the data if it's not already available in the view
            if (!isset($view->getData()['featuredPortfolios'])) {
                $featuredPortfolios = $this->getContextualPortfolios($view);
                $view->with('featuredPortfolios', $featuredPortfolios);
            }
        });

        // Share cities data with the header, footer, and other views
        View::composer(['*'], function ($view) {
            // Only fetch the data if it's not already available in the view
            if (!isset($view->getData()['cities'])) {
                $cities = City::where('status', 'active')
                            ->orderBy('name')
                            ->get();

                $view->with('cities', $cities);
            }
        });
    }

    /**
     * Get contextual portfolios based on the current view context.
     *
     * @param \Illuminate\View\View $view
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getContextualPortfolios($view)
    {
        // Get the current route and parameters
        $currentRoute = Route::current();
        $routeParameters = $currentRoute ? $currentRoute->parameters() : [];
        $currentUri = request()->getPathInfo();

        // Check if we have a service in the view data (service detail page)
        $viewData = $view->getData();
        $service = $viewData['service'] ?? null;

        // If we have a service with a category, use category-based fetching
        if ($service && !empty($service->category)) {
            return $this->getCategoryBasedPortfolios($service->category);
        }

        // Check if we're on a services route by examining the URI
        if (str_starts_with($currentUri, '/services')) {
            // Extract category from route parameters
            $cat = $routeParameters['cat'] ?? '';

            // If we have a category parameter, try to use it for portfolio matching
            if (!empty($cat)) {
                // First check if this category parameter corresponds to a service category
                $categoryService = Service::where('category', $cat)->where('status', 'active')->first();
                if ($categoryService) {
                    return $this->getCategoryBasedPortfolios($cat);
                }

                // If not found as category, check if it's a service SEO URL that has a category
                $serviceByUrl = Service::where('seo_url', $cat)->where('status', 'active')->first();
                if ($serviceByUrl && !empty($serviceByUrl->category)) {
                    return $this->getCategoryBasedPortfolios($serviceByUrl->category);
                }
            }
        }

        // Fallback: Get general portfolios (for home page, city pages, etc.)
        return $this->getFallbackPortfolios();
    }

    /**
     * Get portfolios based on service category with fallback.
     *
     * @param string $category
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getCategoryBasedPortfolios($category)
    {
        // Primary fetch: Get portfolios matching the service category
        $categoryPortfolios = Portfolio::where('status', 'active')
                                    ->where('project_type', $category)
                                    ->orderBy('created_at', 'desc')
                                    ->take(8)
                                    ->get();

        // If we have 8 or more category-matched portfolios, return them
        if ($categoryPortfolios->count() >= 8) {
            return $categoryPortfolios;
        }

        // Secondary fetch: Get additional portfolios to reach 8 total
        $excludeIds = $categoryPortfolios->pluck('id')->toArray();
        $additionalCount = 8 - $categoryPortfolios->count();

        $additionalPortfolios = Portfolio::where('status', 'active')
                                       ->whereNotIn('id', $excludeIds)
                                       ->orderBy('created_at', 'desc')
                                       ->take($additionalCount)
                                       ->get();

        // Combine category portfolios with additional ones
        return $categoryPortfolios->merge($additionalPortfolios);
    }

    /**
     * Get fallback portfolios for non-service contexts.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getFallbackPortfolios()
    {
        return Portfolio::where('status', 'active')
                       ->orderBy('created_at', 'desc')
                       ->take(8)
                       ->get();
    }
}
