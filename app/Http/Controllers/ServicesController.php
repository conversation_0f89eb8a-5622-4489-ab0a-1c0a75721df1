<?php

namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class ServicesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Auth middleware is now applied at the route level
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get all services with proper ordering for the admin panel
        $services = Service::with('parent', 'children', 'category')
                          ->orderBy('parent_id', 'asc')
                          ->orderBy('menu_order', 'asc')
                          ->orderBy('title', 'asc')
                          ->paginate(15);
        return view('admin.services.index', compact('services'));
    }

    /**
     * Display the frontend listing of services.
     */
    public function frontendIndex($cat = '', $seo_url = '')
    {
        // Case 1: Both category and seo_url are provided
        if (!empty($seo_url) && !empty($cat)) {
            // Find category by name first
            $category = \App\Models\Category::where('name', $cat)->where('status', 'active')->first();
            if ($category) {
                $service = Service::where('category_id', $category->id)->where('seo_url', $seo_url)->where('status', 'active')->first();
                if ($service) {
                    return view('services.detail', compact('service'));
                }
            }
            return view("services.{$cat}.{$seo_url}");
        }
        // Case 2: Only one parameter is provided (could be category or seo_url of a top-level service)
        elseif (!empty($cat) && empty($seo_url)) {
            // First check if it's a category
            $category = \App\Models\Category::where('name', $cat)->where('status', 'active')->first();
            if ($category) {
                $services = Service::where('category_id', $category->id)->where('status', 'active')->get();
                if ($services->count() > 0) {
                    return view("services.{$cat}", compact('services'));
                }
            }

            // If not a category, check if it's a seo_url of a top-level service
            $service = Service::where('seo_url', $cat)->whereNull('parent_id')->where('status', 'active')->first();
            if ($service) {
                return view('services.detail', compact('service'));
            }
        }

        // Default: Show all services
        $services = Service::where('status', 'active')->get();

        // Get top parent interior services for the "Types of Interior Design" section
        // Query for interior services that have at least one child
        $interiorServices = Service::whereNull('parent_id')
                                ->where('status', 'active')
                                ->where('is_interior', true)
                                ->has('children', '>=', 1)  // Must have at least one child
                                ->orderBy('menu_order')
                                ->orderBy('title')
                                ->take(6)
                                ->get();

        return view('services.index', compact('services', 'interiorServices'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Define validation rules
        $rules = [
            'title' => 'required|string|max:100',
            'seo_title' => 'required|string|max:100',
            'seo_url' => 'required|string|max:100|unique:services,seo_url',
            'description' => 'required',
            'service_read_more' => 'required',
            'top_banner_image' => 'required|image|mimes:jpeg,png,jpg|max:200',
            'status' => 'required|in:active,inactive',
            'parent_id' => 'nullable|exists:services,id',
            'menu_order' => 'nullable|integer|min:0',
            'show_in_menu' => 'nullable',
        ];

        // Category is required only if parent_id is present
        if ($request->filled('parent_id')) {
            $rules['category_id'] = 'required|exists:categories,id';
        } else {
            $rules['category_id'] = 'nullable|exists:categories,id';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle file upload
        if ($request->hasFile('top_banner_image')) {
            $image = $request->file('top_banner_image');
            $imageName = Str::slug($request->seo_url) . '_' . time() . '.' . $image->getClientOriginalExtension();

            // Check if filename is too long
            if (strlen($imageName) > 100) {
                $imageName = substr($imageName, 0, 90) . '_' . time() . '.' . $image->getClientOriginalExtension();
            }

            // Create directory if it doesn't exist
            $path = public_path('images/services');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $image->move($path, $imageName);
        }

        // Create service
        Service::create([
            'title' => $request->title,
            'category_id' => $request->category_id,
            'seo_title' => $request->seo_title,
            'seo_url' => $request->seo_url,
            'description' => $request->description,
            'service_read_more' => $request->service_read_more,
            'top_banner_image' => $imageName ?? '',
            'status' => $request->status,
            'parent_id' => $request->parent_id,
            'menu_order' => $request->menu_order ?? 0,
            'show_in_menu' => $request->has('show_in_menu') ? true : false,
            'is_interior' => $request->has('is_interior') ? true : false,
            'is_construction' => $request->has('is_construction') ? true : false,
            'is_other' => $request->has('is_other') ? true : false,
        ]);

        return redirect()->route('service.index')
            ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $service = Service::with('category', 'parent', 'children')->findOrFail($id);
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $service = Service::findOrFail($id);
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $service = Service::findOrFail($id);

        // Validate that the service is not trying to set itself as its own parent
        if ($request->parent_id == $id) {
            return redirect()->back()
                ->withErrors(['parent_id' => 'A service cannot be its own parent.'])
                ->withInput();
        }

        // Check if the selected parent is not a child of this service (to prevent circular references)
        if ($request->parent_id) {
            $childIds = $service->children()->pluck('id')->toArray();
            if (in_array($request->parent_id, $childIds)) {
                return redirect()->back()
                    ->withErrors(['parent_id' => 'Cannot select a child service as parent.'])
                    ->withInput();
            }
        }

        // Define validation rules
        $rules = [
            'title' => 'required|string|max:100',
            'seo_title' => 'required|string|max:100',
            'seo_url' => 'required|string|max:100|unique:services,seo_url,' . $id,
            'description' => 'required',
            'service_read_more' => 'required',
            'top_banner_image' => 'nullable|image|mimes:jpeg,png,jpg|max:200',
            'status' => 'required|in:active,inactive',
            'parent_id' => 'nullable|exists:services,id',
            'menu_order' => 'nullable|integer|min:0',
            'show_in_menu' => 'nullable',
        ];

        // Category is required only if parent_id is present
        if ($request->filled('parent_id')) {
            $rules['category_id'] = 'required|exists:categories,id';
        } else {
            $rules['category_id'] = 'nullable|exists:categories,id';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle file upload
        if ($request->hasFile('top_banner_image')) {
            // Delete old image if exists
            if ($service->top_banner_image && File::exists(public_path('images/services/' . $service->top_banner_image))) {
                File::delete(public_path('images/services/' . $service->top_banner_image));
            }

            $image = $request->file('top_banner_image');
            $imageName = Str::slug($request->seo_url) . '_' . time() . '.' . $image->getClientOriginalExtension();

            // Check if filename is too long
            if (strlen($imageName) > 100) {
                $imageName = substr($imageName, 0, 90) . '_' . time() . '.' . $image->getClientOriginalExtension();
            }

            // Create directory if it doesn't exist
            $path = public_path('images/services');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $image->move($path, $imageName);
            $service->top_banner_image = $imageName;
        }

        // Update service
        $service->title = $request->title;
        $service->category_id = $request->category_id;
        $service->seo_title = $request->seo_title;
        $service->seo_url = $request->seo_url;
        $service->description = $request->description;
        $service->service_read_more = $request->service_read_more;
        $service->status = $request->status;
        $service->parent_id = $request->parent_id;
        $service->menu_order = $request->menu_order ?? 0;
        $service->show_in_menu = $request->has('show_in_menu') ? true : false;
        $service->is_interior = $request->has('is_interior') ? true : false;
        $service->is_construction = $request->has('is_construction') ? true : false;
        $service->is_other = $request->has('is_other') ? true : false;
        $service->save();

        return redirect()->route('service.index')
            ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $service = Service::findOrFail($id);

        // Delete image if exists
        if ($service->top_banner_image && File::exists(public_path('images/services/' . $service->top_banner_image))) {
            File::delete(public_path('images/services/' . $service->top_banner_image));
        }

        $service->delete();

        return redirect()->route('service.index')
            ->with('success', 'Service deleted successfully.');
    }
}
