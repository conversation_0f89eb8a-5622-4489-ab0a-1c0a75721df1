<?php

namespace App\Http\Controllers;

class MainController extends Controller
{
    public function home(){
        return view('home');
    }

    public function about(){
        return view('about');
    }

    public function reviews(){
        return view('reviews');
    }

    public function contactUs(){
        return view('contact-us');
    }

    public function privacyPolicy(){
        return view('privacy-policy');
    }

    public function termsConditions(){
        return view('terms-conditions');
    }

    public function getEstimate($type = ''){
        $type = $type == 'Construction' ? 'Construction' : 'Interior';
        return view('estimate',['type' => $type]);
    }
}
