<?php

namespace App\Http\Controllers;

use App\Mail\LeadConfirmation;
use App\Mail\TeamLeadNotification;
use App\Models\Lead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class LeadsController extends Controller
{
    /**
     * Store a newly created lead in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'clientname' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'propertylocation' => 'nullable|string|max:255',
            'othersCity' => 'nullable|string|max:255',
            'project-needs' => 'nullable|string|max:255',
        ]);


        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Get the form data
            $name = $request->input('clientname');
            $phone = $request->input('phone');
            $email = $request->input('email');
            $city = $request->input('propertylocation');
            $othersCity = $request->input('othersCity');
            $projectNeeds = $request->input('project-needs');
            $url = $request->input('url');
            $utmSource = $request->input('utm_source');
            $utmMedium = $request->input('utm_medium');
            $utmCampaign = $request->input('utm_campaign');
            $utmTerm = $request->input('utm_term');


            // Create new lead
            $lead = Lead::create([
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'city' => $city,
                'othersCity' => $othersCity,
                'project_needs' => $projectNeeds,
                'url' => $url,
                'utm_source' => $utmSource,
                'utm_medium' => $utmMedium,
                'utm_campaign' => $utmCampaign,
                'utm_term' => $utmTerm,
            ]);


            // Determine if the submission is from the estimate page or enquiry form
            $isEstimatePage = strpos($request->input('url'), 'get-estimate') !== false;
            $formType = $isEstimatePage ? 'estimate' : 'enquiry';

            // Prepare lead data for emails
            $leadData = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'city' => $city,
                'othersCity' => $othersCity,
                'project_needs' => $projectNeeds,
                'url' => $url,
                'utm_source' => $utmSource,
                'utm_medium' => $utmMedium,
                'utm_campaign' => $utmCampaign,
                'utm_term' => $utmTerm
            ];

            // Send confirmation email to user if email is provided
            if ($email) {
                try {
                    // Queue the email instead of sending it immediately
                    Mail::to($email)->queue(new LeadConfirmation($leadData, $formType));
                    \Log::info('Confirmation email queued for user: ' . $email);
                } catch (\Exception $emailEx) {
                    \Log::error('Error queuing confirmation email to user:', ['error' => $emailEx->getMessage()]);
                    // Continue execution even if email fails
                }
            }

            // Send notification email to team
            try {
                // Get team email from config or use a default
                $teamEmail = config('mail.team_email', '<EMAIL>');

                // Queue the email instead of sending it immediately
                Mail::to($teamEmail)->queue(new TeamLeadNotification($leadData, $formType));
                \Log::info('Notification email queued for team: ' . $teamEmail);
            } catch (\Exception $teamEmailEx) {
                \Log::error('Error queuing notification email to team:', ['error' => $teamEmailEx->getMessage()]);
                // Continue execution even if email fails
            }

        } catch (\Exception $e) {
            \Log::error('Error creating lead:', ['error' => $e->getMessage()]);
            return redirect()->back()
                ->with('error', 'Error saving your information. Please try again.')
                ->withInput();
        }

        // Determine form type again (in case it wasn't set in the try block)
        $isEstimatePage = strpos($request->input('url'), 'get-estimate') !== false;

        // Set success message based on form type
        $successMessage = $isEstimatePage
            ? 'Thank you for your interest. Our team will provide you with a detailed estimate shortly.'
            : 'Thank you for your enquiry. We will contact you shortly.';

        // Add email confirmation message if email was provided
        if ($email) {
            $successMessage .= ' A confirmation has been sent to your email address.';
        }

        return redirect()->back()->with('success', $successMessage);
    }
}
