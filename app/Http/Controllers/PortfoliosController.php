<?php

namespace App\Http\Controllers;

use App\Models\Portfolio;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class PortfoliosController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Auth middleware is applied at the route level for admin routes
    }

    /**
     * Display the frontend portfolio listing or detail page.
     *
     * @param string $seo_url
     * @return \Illuminate\View\View
     */
    public function portfolio($seo_url = '')
    {
        if ($seo_url == '') {
            // Get all active portfolios for the list page
            $portfolios = Portfolio::where('status', 'active')->orderBy('created_at', 'desc')->get();
            return view('portfolios.list', compact('portfolios'));
        }

        // Get the specific portfolio for the detail page
        $portfolio = Portfolio::where('seo_url', $seo_url)->where('status', 'active')->firstOrFail();

        // Get other portfolios for the "Other projects" section
        $otherPortfolios = Portfolio::where('id', '!=', $portfolio->id)
            ->where('status', 'active')
            ->inRandomOrder()
            ->limit(4)
            ->get();

        return view('portfolios.detail', compact('portfolio', 'otherPortfolios'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $portfolios = Portfolio::orderBy('id', 'asc')->paginate(10);
        return view('admin.portfolios.index', compact('portfolios'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.portfolios.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'description' => 'required',
            'project_type' => 'required|in:rooms,kitchen,living,in-progress,office,commercial',
            'featured_image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Generate SEO URL
        $seoUrl = Str::slug($request->title);

        // Check if SEO URL already exists
        $count = Portfolio::where('seo_url', $seoUrl)->count();
        if ($count > 0) {
            $seoUrl = $seoUrl . '-' . ($count + 1);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $featuredImage = $request->file('featured_image');
            $featuredImageName = $seoUrl . '-featured-' . time() . '.' . $featuredImage->getClientOriginalExtension();

            // Create directory if it doesn't exist
            $path = public_path('images/portfolios');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $featuredImage->move($path, $featuredImageName);
        }

        // Create portfolio
        $portfolio = Portfolio::create([
            'title' => $request->title,
            'description' => $request->description,
            'project_type' => $request->project_type,
            'seo_url' => $seoUrl,
            'featured_image' => $featuredImageName ?? '',
            'status' => $request->status,
        ]);

        // Handle multiple image uploads
        if ($request->hasFile('images')) {
            $imageNames = [];
            $sortOrder = 0;
            foreach ($request->file('images') as $image) {
                $imageName = $seoUrl . '-' . time() . '-' . $sortOrder . '.' . $image->getClientOriginalExtension();
                $image->move($path, $imageName);
                $imageNames[] = $imageName;
                $sortOrder++;
            }

            // Store image paths as comma-separated values
            $portfolio->images = implode(',', $imageNames);
            $portfolio->save();
        }

        return redirect()->route('portfolio.index')
            ->with('success', 'Portfolio created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $portfolio = Portfolio::findOrFail($id);
        return view('admin.portfolios.show', compact('portfolio'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $portfolio = Portfolio::findOrFail($id);
        return view('admin.portfolios.edit', compact('portfolio'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $portfolio = Portfolio::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'description' => 'required',
            'project_type' => 'required|in:rooms,kitchen,living,in-progress,office,commercial',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Generate SEO URL if title has changed
        if ($portfolio->title != $request->title) {
            $seoUrl = Str::slug($request->title);

            // Check if SEO URL already exists
            $count = Portfolio::where('seo_url', $seoUrl)
                ->where('id', '!=', $id)
                ->count();

            if ($count > 0) {
                $seoUrl = $seoUrl . '-' . ($count + 1);
            }

            $portfolio->seo_url = $seoUrl;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($portfolio->featured_image && File::exists(public_path('images/portfolios/' . $portfolio->featured_image))) {
                File::delete(public_path('images/portfolios/' . $portfolio->featured_image));
            }

            $featuredImage = $request->file('featured_image');
            $featuredImageName = $portfolio->seo_url . '-featured-' . time() . '.' . $featuredImage->getClientOriginalExtension();

            // Create directory if it doesn't exist
            $path = public_path('images/portfolios');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $featuredImage->move($path, $featuredImageName);
            $portfolio->featured_image = $featuredImageName;
        }

        // Update portfolio
        $portfolio->title = $request->title;
        $portfolio->description = $request->description;
        $portfolio->project_type = $request->project_type;
        $portfolio->status = $request->status;
        $portfolio->save();

        // Handle multiple image uploads
        if ($request->hasFile('images')) {
            // Get existing images array
            $existingImages = $portfolio->images_array;
            $sortOrder = count($existingImages);

            // Add new images
            $newImages = [];
            foreach ($request->file('images') as $image) {
                $imageName = $portfolio->seo_url . '-' . time() . '-' . $sortOrder . '.' . $image->getClientOriginalExtension();

                // Create directory if it doesn't exist
                $path = public_path('images/portfolios');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                }

                $image->move($path, $imageName);
                $newImages[] = $imageName;
                $sortOrder++;
            }

            // Merge existing and new images
            $allImages = array_merge($existingImages, $newImages);

            // Update portfolio with all images
            $portfolio->images = implode(',', $allImages);
            $portfolio->save();
        }

        return redirect()->route('portfolio.index')
            ->with('success', 'Portfolio updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $portfolio = Portfolio::findOrFail($id);

        // Delete featured image if exists
        if ($portfolio->featured_image && File::exists(public_path('images/portfolios/' . $portfolio->featured_image))) {
            File::delete(public_path('images/portfolios/' . $portfolio->featured_image));
        }

        // Delete all portfolio images
        foreach ($portfolio->images_array as $imagePath) {
            if (File::exists(public_path('images/portfolios/' . $imagePath))) {
                File::delete(public_path('images/portfolios/' . $imagePath));
            }
        }

        $portfolio->delete();

        return redirect()->route('portfolio.index')
            ->with('success', 'Portfolio deleted successfully.');
    }

    /**
     * Remove a specific portfolio image.
     *
     * @param  string  $portfolioId
     * @param  string  $imageName
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteImage(string $portfolioId, string $imageName)
    {
        $portfolio = Portfolio::findOrFail($portfolioId);

        // Delete image file
        if (File::exists(public_path('images/portfolios/' . $imageName))) {
            File::delete(public_path('images/portfolios/' . $imageName));
        }

        // Remove image from the portfolio's images list
        $portfolio->removeImage($imageName);

        return redirect()->route('portfolio.edit', $portfolioId)
            ->with('success', 'Image deleted successfully.');
    }
}
