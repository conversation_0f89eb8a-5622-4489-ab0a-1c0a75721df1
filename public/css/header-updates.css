/*
 * Header Color Updates
 * This file contains color overrides for the header, mobile header, and top-contact sections
 */

/* Main Header Background Update */
.header-wrap {
  background-color: var(--space-cadet) !important;
  box-shadow: 1px 0 3px #222;
}

/* Mobile Header Background Update */
header.header-mobile {
  background-color: var(--space-cadet) !important;
}

/* Top Contact Background Update */
.top-contact {
  background-color: var(--hunyadi-yellow) !important;
  padding: 12px 0;
}

/* Navigation Link Colors for Main Header */
.menu-desktop ul.ul--inline.ul--no-style > li > a {
  color: #fff !important;
  font-weight: 500;
}

.menu-desktop ul.ul--inline.ul--no-style > li > a:hover {
  color: var(--hunyadi-yellow) !important;
}

/* Navigation Link Colors for Mobile Header */
.menu-mobile {
  background-color: #fff !important;
}

.menu-mobile ul.ul--no-style > li > a {
  color: var(--space-cadet) !important;
}

.menu-mobile ul.ul--no-style > li > a:hover {
  color: var(--hunyadi-yellow) !important;
}

/* Mobile menu more icon */
.menu-mobile__more, .fa.fa-plus.menu-mobile__more {
  color: var(--space-cadet) !important;
}

/* Top Contact Text Colors */
.top-contact--left > span {
  color: var(--space-cadet) !important;
  font-weight: 500;
}

.top-contact--left > span > a {
  color: var(--space-cadet) !important;
}

.top-contact--left > span > a:hover {
  color: #fff !important;
}

/* Top Contact Social Icons */
.top-contact--right a i {
  color: var(--space-cadet) !important;
}

.top-contact--right a i:hover {
  color: #fff !important;
}

/* Dropdown Menu Background */
.dropdown-menu, .dropdown-menu-1 {
  background-color: var(--space-cadet) !important;
  border-color: var(--ultra-violet) !important;
}

.dropdown-menu li a, .dropdown-menu-1 li a {
  color: #fff !important;
}

.dropdown-menu li a:hover, .dropdown-menu-1 li a:hover {
  color: var(--hunyadi-yellow) !important;
  background-color: var(--ultra-violet) !important;
}

/* Mobile Menu Dropdown Background */
.menu-mobile ul.ul--no-style ul.ul--no-style {
  background-color: #fff !important;
}

/* Mobile Menu Dropdown Text Color */
.menu-mobile ul.ul--no-style ul.ul--no-style li a {
  color: var(--space-cadet) !important;
}

.menu-mobile ul.ul--no-style ul.ul--no-style li a:hover {
  color: var(--hunyadi-yellow) !important;
}

/* Get Free Estimate Button */
header .click-on {
  background-color: var(--hunyadi-yellow) !important;
  color: var(--space-cadet) !important;
  font-weight: 600;
}

header .click-on:hover {
  background-color: #fff !important;
  color: var(--space-cadet) !important;
}

/* Mobile Get Free Estimate Button */
.header-mobile ul li .click-on-2 {
  background-color: var(--hunyadi-yellow) !important;
  color: var(--space-cadet) !important;
  font-weight: 600;
}

.header-mobile ul li .click-on-2:hover {
  background-color: #fff !important;
  color: var(--space-cadet) !important;
}

/* Logo area adjustments for better visibility */
/* .header-wrap .logo a, .header-mobile .logo-mob a {
  background-color: #fff;
  display: inline-block;
  border-radius: 5px;
  padding: 5px;
} */

/* Menu toggle button color */
.menu-mobile__button i {
  color: #fff !important;
}

/* Ensure dropdown carets are visible */
.header-desktop .caret {
  color: #fff !important;
}

/* Mobile menu caret color */
.menu-mobile .caret {
  color: var(--space-cadet) !important;
}

/* Media queries to ensure mobile responsiveness */
@media (max-width: 576px) {
  .top-contact {
    display: block !important; /* Override the display:none in the original CSS */
  }
}
