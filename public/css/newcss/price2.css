


.form-check-input {
  margin-right: 6px;
}

.list-group-item {
  display: flex;
  align-items: center;
  padding: 7px;
  width: 50%;
  margin: 10px auto;
  border: 2px solid #ccc;
}

.list-group-item + .list-group-item {
  border-top: 2px solid #ccc;
}
/*.row.justify-content-center {*/
/*  width: 100%;*/
/*}*/
@media (max-width:500px) {
    .list-group-item{
    width: 70%;
    }
}
.btn-next-red {
    background: #f47e45;
    color: #ffffff;
    cursor: pointer;
    padding: 8px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 60px;
}


/* Remove blue outline when button is focused */
.btn-next-red:focus {
  outline: none;
}

/* Remove blue color on hover */
.btn-next-red:hover {
  background-color: red; /* You can keep the same color or change it if needed */
  color: white;
}

.btn-counter {
    background-color: red;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    line-height: 20px;
    text-align: center;
    font-size: 16px;
    padding: 0;
    /* Remove blue outline on button click */
    outline: none;
  }

  /* Remove blue outline on button click */
  .btn-counter:focus {
    outline: none;
  }

  /* Customize hover effect */
  .btn-counter:hover {
    background-color: none;
    color : white;
    outline : none;
    /* Adjust other styles as needed */
  }

  /* Center the counter value */
  .counter-wrap {
    display: flex;
    align-items: center;
  }

  .counter-value {
    margin: 0 8px;
  }
.form-check-input {
  margin-right: 6px;
}

.list-group-item {
  display: flex;
  align-items: center;
  padding: 7px;
  width: 80%;
  margin: 10px auto;
  border: 2px solid #ccc;
}

.list-group-item + .list-group-item {
  border-top: 2px solid #ccc;
}
.row.justify-content-center {
  width: 100%;
  margin-left : 0px;
  margin-rigth : 0px;
}



/* Remove blue outline when button is focused */
.btn-next-red:focus {
  outline: none;
}

/* Remove blue color on hover */
.btn-next-red:hover {
  background-color: #1b375d; /* You can keep the same color or change it if needed */
  color: white;
}

/* diffrent row data */

.labels {
	color: black;
}

.content-div2 {
	display: none;
}

/* Media query for tablets (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .col-md-5 {
    flex: 0 0 83.333333%; /* Adjust the width as needed */
    max-width: 83.333333%;
  }
  .content-div2 {text-align : center;}
}

@media (max-width: 767px) {
    
    .content-div1 {
    margin-bottom: 40px !important;
    margin-top: 80px !important;
}
}

        

	@media (max-width: 800px) {
		/*.content-div1 {*/
		/*	display: none;*/
		/*}*/
		
		.resp-btn {
		    
		    display: inline !important;
		}

		.content-div2 {
			display: block;
		}

		.content-div2 {
			margin-top: 8rem;
		}
	}

@media (max-width : 380px){
    .list-group-item{
        font-size : 10px;
    }
    .btn-counter{
        font-size : 10px;
    }
}

.content-div1 {
	bottom: 0;
	margin-top: 20px;
	width: 100%;
}

.content-div01{
    display : none;
}

.sections {
	background-image: url("/images/estimate_page_bb.jpg");
	background-size: cover;
	background-position: center center;
}

.store {
	display: flex;
	align-items: center;
	justify-content: center;
	background-size: cover;
	/*height: 100%;*/
	/*min-height: 100vh;*/
	/* Ensure the section takes at least the full viewport height */
    padding: 2rem 0px 30px 0px;
}
	@media (max-width: 1000px) {
/*.content-div1{*/
/*    display : none;*/
/*}*/
	.content-div2 {
			display: block;
		}
}
	




.content-div h2 {
	margin-top: 20px;
	/* Add margin-top for the first div */
}

.content-color {
	color: white;
}

.content-red {
	color: #f47e45;
}
.content-div,
.form-div {
	background-color: white;
	border-radius: 10px;
	padding: 20px;
}
.form-group-container {
    display: flex;
    flex-direction: column; /* Stack fields vertically */
    align-items: center; /* Center fields horizontally */
  
}
.input-container {
    position: relative;
    margin-bottom: 20px; /* Adjust spacing as needed */
}

#google-signin-button{

    padding: 10px 20px; /* Adjust padding as needed */
    border-radius: 4px; /* Optional: Add rounded corners */
   
}



.input-container input {
    /*width: 100%;*/
    min-width : 20rem;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    transition: padding-top 0.2s ease, border-color 0.2s ease;
}

.input-container label {
    position: absolute;
    top: 50%;
    left: 10px;
    padding: 0 4px;
    background-color: #fff;
    transition: all 0.2s ease;
    pointer-events: none;
    color: #495057;
    font-size: 14px;
    transform: translateY(-50%);
}

.input-container input:focus + label,
.input-container input:not(:placeholder-shown) + label {
    top: -1px;
    left : 0px;
    font-size: 12px;
    color: #007bff;
    background-color: #fff;
    border-radius : 3px;
    color:"grey";
    padding: 0 4px;
}

.input-container input:focus {
    border-color: #007bff;
    outline: none;
}

/* Styles for the flag icon in phone number field */
.phone-number-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container {
    position: relative;
    width: 100%;
}
.phone-number-container .input-container input {
    width: 100%;
    padding: 10px;
    padding-left: 40px; /* Adjust padding to make space for the flag icon */
    box-sizing: border-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    transition: padding-top 0.2s ease, border-color 0.2s ease;
}

.flag-icon {
    position: absolute;
    left: 10px; /* Position the flag inside the input field */
    top: 50%;
    transform: translateY(-50%);
}

.flag-icon img {
    width: 20px; /* Adjust flag size */
    height: auto;
}




.input-container input:focus {
    border-color: #007bff;
    outline: none;
}


.custom-google-icon {
    width: 9rem;
    height: 4rem;
    margin-top : 10px;
}

.custom-google-icon:hover {
    cursor: pointer; /* Remove quotes around 'pointer' */
}


.custom-form-control {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.custom-invalid-feedback {
    color: #dc3545;
    font-size: 80%;
    display: block;
    margin-top: 5px;
}

.custom-modal-dialog {
    position: relative;
    width: 100%;
    max-width: 500px; /* Control modal width */
    margin: auto;
}

