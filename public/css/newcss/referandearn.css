body,html {
  margin: 0;
  font-size: 100%;
  background: #fff;
  font-family: Lato,sans-serif;
  scroll-behavior: smooth;
}
.ShowHide {overflow: hidden;font-family: Arial, Helvetica, sans-serif;background-color: #f6f3f3;padding: 10px 10px;color: #000;}
#left {overflow: hidden;text-align: center;height: 50px;}
#left p{margin-bottom: 0px;letter-spacing: 1px;}
#left a{color: #d60000 !important;}
#left a:hover{color: #d60000;text-decoration: none;font-weight: 700;}
#right {float: right;width: 30px;height: 20px;text-align: center;}
#right a:hover{color: #000; text-decoration: none;font-weight: 600;}

.post-lockdown,.post-lockdown1{font-family: Roboto,sans-serif;}
.post-lockdown .row, .post-lockdown .col-md-12 {padding: 0px;margin: 0px;}
.post-lockdown1,.post-lockdown2,.post-lockdown3,.post-lockdown4,.post-lockdown5,.post-lockdown6{padding: 20px 20px;}
.post-lockdown1 img{width: 400px;}
.lockdowndata {padding: 50px 50px;}
.lockdowndata h2.mainheading{font-size: 25px;text-transform: capitalize;font-weight: 700;}
.lockdowndata p.subheading{font-size: 23px;font-weight: 700;}
.lockdowndata p.subheading span{color:Blue;}
.lockdowndata p.data{font-size: 20px;}
.lockdowndata p.data span{color: #d60000;}
.lockdowndata .border1{border-bottom: 10px solid #d60000 !important;
    width: 86px;
    margin-bottom: 20px;
    margin-left: 0px;
    margin-top: 0px;
    border-radius: 10px;
  }
.stickyForm span.error{
    color:#d60000;
}
.whatsapplink1{    
  position:fixed;
  width:50px;
  height:50px;
  bottom:83%;
  right:20px;
  background-color:#25d366;
  color:#FFF !important;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}
.my-float{
  margin-top:10px;
}
.moreicon .oneicon,.moreicon .twoicon{
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-bottom: 10px;
    color: #fff;
    text-align: center;
    font-size: 40px;
    text-shadow: 2px 2px #000;
}
.moreicon .oneicon p, .moreicon .twoicon p{
  font-size: 20px;
  color: #fff;
  text-shadow: 2px 1px #000;
}
.moreicon .oneicon{
  bottom: 0%;
  left: -20%;
}
.moreicon .twoicon{
  bottom: 0%;
  left: 20%;
}
body a {
  text-decoration: none;
  transition: .5s all;
  -webkit-transition: .5s all;
  -moz-transition: .5s all;
  -o-transition: .5s all;
  -ms-transition: .5s all;
}
.btn:focus, .btn.focus{
  box-shadow: none;
}
.mail a {
  text-transform: lowercase;
  padding: 10px 5px;
  border: 2px solid #d60000;
}
a:hover {
  text-decoration: none;
}
.textimage {
    margin-left: 90px;
  /*text-align: center;*/
}
.textimage h2 {
  color: #d60000;
  font-size: 40px;
  /*letter-spacing: 2px;*/
  font-weight: 700;
}
.textimage h2>span{
  color: #d60000;
}
.textimage h3 {
  color: #000;
  font-size: 20px;
  letter-spacing: 2px;
  line-height: 40px;
  text-shadow: 0px 0px #000;
  font-weight: 500;
}
.monthcontent{
  box-shadow: 3px 3px 5px 6px #ccc;
  background: url(../images/istockphoto-1490571644-612x612.jpg) no-repeat center;background-size: cover;-webkit-background-size: cover;-moz-background-size: cover;-o-background-size: cover;-ms-background-size: cover;
  height: 420px;
  background-position-y: top;
}

.monthcontent .monthdata{
    /*padding-top: 110px;
    padding-bottom: 100px;*/
    color: #fff !important;
    text-align: center;
    background: rgba(0,0,0,0.4);
    padding: 100px 50px 100px 50px;
    border-radius: 50px;
}
.monthcontent .monthdata p{
  color: #fff !important;
  font-size: 22px;
  text-shadow: 2px 1px #000;
}
.monthcontent .monthdata button{
  text-align: center;
  margin: auto;
  margin-top: 15px;
}
.social_section_1info li.facebook a{
  color: #3b5998;
}
ul.social_section_1info li.facebook a:hover{
  background-color: #3b5998;
}
ul.social_section_1info li.twitter a:hover{
  background-color: #55acee;
}
ul.social_section_1info li.instragram a:hover{
  background-color: #bc2a8d;
}
ul.social_section_1info li.linkedin a:hover{
  background-color: #0077b5;
}
ul.social_section_1info a span.fa:hover{
  color: #fff;
}
h1,h2,h3,h4,h5,h6 {
  margin: 0;
  letter-spacing: .5px;
}
p {
  margin: 0;
  font-size: 15px;
  line-height: 2em;
  letter-spacing: 1px;
  color: #666;
}
ul {
  margin: 0;
  padding: 0;
}
header {
  position: absolute;
  width: 100%;
  padding: 5px 0;
  z-index: 9;
}
.right-p li,.right-p li a {
  display: inline-block;
  color: #fff;
  font-size: 15px;
  letter-spacing: 2px;
}
.right-p li span {
  color: #fff;
}
.toggle,[id^=drop] {
  display: none;
}
nav {
  margin: 0;
  padding: 0;
}
#logo img,.footer-title img {
  width: 300px;
}
#logo a {
  float: left;
  display: initial;
  margin-left: 67px;
  color: #fff;
  padding: 0 0;
  border: none;
}
nav:after {
  content: "";
  display: table;
  clear: both;
}
nav ul {
  float: right;
  padding: 0;
  margin: 0;
  list-style: none;
  position: relative;
}
nav ul li {
  margin-right: 1px;
  display: inline-block;
  float: left;
}
nav a {
  color: #000;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-left: 0;
  padding-right: 0;
  padding: 10px 13px;
  font-weight: 400;
  font-size: 15px;
  vertical-align: middle;
}
nav a:hover {
  color: #000;
  font-weight: 600;
}
li>a:only-child:after {
  content: '';
}

@media all and (max-width :991px) {
  #logo {
    display: block;
    padding: 0;
    width: 100%;
    text-align: center;
    float: none;
  }
  .menu li.active a {
    color: #3369e7;
  }
  nav ul li span {
    color: #333;
  }
  nav {
    margin: 0;
  }
  nav a {
    color: #333;
  }
  .menu,.toggle+a {
    display: none;
  }
  .toggle {
    display: none;
    padding: 5px 15px;
    font-size: 20px;
    text-decoration: none;
    border: none;
    float: right;
    background-color: #d60000;
    color: #fff;
    margin-bottom: 0;
  }
  .menu .toggle {
    float: none;
    text-align: center;
    margin: auto;
    width: 30%;
    padding: 5px;
    font-weight: 400;
    font-size: 15px;
    letter-spacing: 1px;
  }
  .toggle:hover {
    color: #333;
    background-color: #fff;
  }
  [id^=drop]:checked+ul {
    display: block;
    background: #fff;
    padding: 15px 0;
    width: 100%;
    text-align: center;
  }
  nav ul li {
    display: block;
    width: 100%;
    padding: 7px 0;
  }
  nav a {
    padding: 5px 0;
  }
  nav a:hover {
    color: #333;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -20%;
    left: 40%;
  }
  .moreicon .twoicon{
    bottom: -20%;
    left: -10%;
  }
}


.banner-text-w3pvt {
  padding: 16vw 0 11vw;
  box-sizing: border-box;
}
h2.heading {
  font-size: 30px;
  text-transform: capitalize;
  color: #333;
  font-weight: 600;
}
.services-inner {
  /*margin-left: 35px;*/
  text-align: center;
  /*transition: .3s;*/
  background: #fff;
}
/*.our-services-img {
  float: left;
  margin-left: -36px;
  margin-right: 22px;
  margin-top: 28px;
}*/
.our-services-img span {
  font-size: 30px;
}
/*.services-inner:hover{
  transform: scale(1.1);
}*/
/*.our-services-text {
  padding-right: 10px;
}
.our-services-text {
  overflow: hidden;
  padding: 28px 0 25px;
}*/
/*.our-services-wrapper:hover .services-inner {
  background: #fff none repeat scroll 0 0;
}*/
.services-inner .newimg img {
  width: 30%;
}
.our-services-text h5 {
  font-weight: 600;
}
.our-services-text p {
  margin-bottom: 0;
}
.mb-5, .my-5{
  margin-bottom: 2% !important;
}
.services p {
  color: #000;
}
.services1 p{
  color: #000;
  text-align: center;
}
.services {
  position: relative;
  z-index: 2;
}
h3.heading, h1.heading {
  font-size: 30px;
  text-transform: capitalize;
  color: #333;
  font-weight: 600;
  text-align: center;
}
.testimonials {
  background-color: #f5f5f5;
  box-shadow: 3px 3px 5px 6px #ccc;
}
.testi-info-text p {
  letter-spacing: 1px;
  width: 100%;
  margin: 0 auto 0;
  color: #000;
  line-height: 30px;
  padding: 1em 1em;
  font-size: 15px;
}
.testi-pos h4 {
  text-transform: uppercase;
  font-size: 1em;
  color: #000;
  font-weight: 600;
  letter-spacing: 2px;
}
.subscribe{
  background-color: #f5f5f5;
  text-align: center;
  box-shadow: 3px 3px 5px 6px #ccc;
  margin: auto;
  width: 70%;
}
.subscribe .subscribe-right span{
  color: #d60000;
}
.footer-layer {
  background-color: #fff;
  box-shadow: 3px 3px 5px 6px #ccc;
}
.footer-grid_section {
  width: 90%;
  margin: auto;
}
.footer-text p {
  color: #000;
}
ul.social_section_1info {
  margin-top: 20px;
}
ul.social_section_1info li {
  display: inline-block;
}
ul.social_section_1info a {
    cursor:pointer;
  margin-right: 4px;
  width: 40px;
  height: 40px;
  display: block;
  text-align: center;
  line-height: 40px;
  border: 1px solid #000;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}
ul.social_section_1info a span.fa {
  font-size: 20px;
  color: #000;
  line-height: 40px;
}
ul.links li {
  display: inline-block;
  padding: 0 10px;
}
.copy-right p,.copy-right p a,ul.links a {
  color: #777;
  letter-spacing: 1px;
  text-transform: capitalize;
}
.copyright {
  background: #111;
}
.copy-right {
  text-align: right;
}
.copy-right p a:hover {
  color: #fff;
}
.image {
  width: 20%;
}
.subscribe h4 {
  padding-bottom: 20px;
  text-align: center;
  font-size: 25px;
}
.subscribe {
  text-align: center;
}
.subscribe a {
  padding: 5px 10px 5px 10px;
  background-color: #d60000;
  color: #fff;
}
.subscribe-left img {
  margin-top: 5%;
  margin-bottom: 5%;
}
.subscribe-left {
  text-align: center;
}
/*.newimg {
  margin-top: 20px;
  margin-right: 11px;
}*/

.modal-dialog {
  position: fixed;
  top: 200px;
  left: 25%;
  width: 50%;
  background: rgba(0,0,0,0.8);
  color: #fff;
  box-shadow: 10px 10px 5px #aaaaaa;
  z-index: 9;
  border-radius: 20px;
}
.modal .modal-content {
  padding: 20px 20px 20px 20px;
  -webkit-animation-name: modal-animation;
  -webkit-animation-duration: 0.5s;
  animation-name: modal-animation;
  animation-duration: 0.5s;
}
  
@-webkit-keyframes modal-animation {
  from {
    top: -100px;
    opacity: 0;
  }
  to {
    top: 0px;
    opacity: 1;
  }
}
  
@keyframes modal-animation {
  from {
    top: -100px;
    opacity: 0;
  }
  to {
    top: 0px;
    opacity: 1;
  }
}

.popupmodallead form {
  width: 80%;
  margin: auto;
}
.popupmodallead form input[type="text"], .popupmodallead form input[type="email"], .popupmodallead form input[type="tel"], .popupmodallead form textarea, .popupmodallead form .intl-tel-input {
  margin-bottom: 10px;
  background: transparent;
}
.popupmodallead button.close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: navajowhite;
  color: #fff;
  font-size: 30px;
}

@media(max-width:1080px) {
  .navbar-light .navbar-nav .nav-link {
    padding: 0 0 5px;
  }
  .scrollform{
    top:11%;
  }
  .banner-form-w3 h3 {
    font-size: 17px;
  }
  .our-services-img {
    margin-right: 8px;
  }
  .newimg {
    margin-right: 0;
  }
  .footer-grid_section {
    width: 75%;
  }
  .copy-right p,.copy-right p a,ul.links a {
    letter-spacing: .5px;
  }
  .banner-text-w3pvt {
    padding: 19vw 0 15vw;
  }
  nav a {
    padding: 10px 10px;
    font-size: 14px;
  }

  #logo a {
    font-size: .6em;
  }

  .footer-title a {
    font-size: 28px;
  }

  .subscribe h4 {
    font-size: 25px;
  }
  .monthcontent .monthdata {
    padding: 100px 50px 100px 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
}
@media(max-width:1441px){
  .scrollform{
    top:11%;
  }
}
@media(max-width:1024px) {
  .testi-info-text h4 {
    font-size: 20px;
    letter-spacing: 1px;
  }
  .scrollform{
    top:27%;
  }
  .monthcontent .monthdata {
    padding: 100px 50px 100px 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
}

@media(max-width:991px) {
  .banner-text-w3pvt {
    padding: 19vw 0 8vw;
  }
  .testi-info-text p {
    padding: 1em 0;
    font-size: 14.5px;
  }
  .footer-grid_section {
    width: 100%;
  }
  .links {
    text-align: center;
  }
  .copy-right {
    text-align: center;
    margin-top: 10px;
  }
  .bottom-grids .col-md-3 {
    padding: 0 5px;
  }
  .image {
    width: 5%;
  }
  .monthcontent .monthdata {
    padding: 100px 50px 100px 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
}

@media(max-width:900px) {
  h2.heading {
    font-size: 27px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -25%;
    left: 40%;
  }
  .moreicon .twoicon{
    bottom: -25%;
    left: -10%;
  }
  .modal-dialog {
    width: 50%;
  }
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:768px) {
  #logo img, .footer-title img{
    width: 250px;
  }
  .padding{
    display: none;
  }
  .footer-title a {
    font-size: 27px;
  }
  header{
    background: rgba(0,0,0,.1);
  }
  .testi-info-text h4 {
    font-size: 20px;
    letter-spacing: 1px;
  }
  .banner-text-w3pvt {
    padding: 35vw 0 3vw;
  }
  .subscribe-left img {
    margin-top: 2%;
  }
  .monthcontent .monthdata {
    padding: 100px 50px 100px 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -25%;
    left: -15%;
  }
  .moreicon .twoicon{
    bottom: -25%;
    left: 40%;
  }
  .modal-dialog {
    width: 50%;
  }
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:736px) {
  .image {
    width: 7%;
  }
  .scrollform{
    top:35%;
  }
  .banner-text-w3pvt {
    padding: 30vw 0 8vw;
  }
  h2.heading {
    font-size: 23px;
  }
  .subscribe-left img {
    margin-top: 2%;
  }
  .layer {
    height: 830px;
  }
  .monthcontent .monthdata {
    padding: 100px 50px 100px 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -20%;
    left: -40%;
  }
  .moreicon .twoicon{
    bottom: -20%;
    left: 40%;
  }
  .modal-dialog {
    width: 50%;
  }
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:667px) {
  .subscribe-left img {
    margin-top: 5%;
    width: 60%;
  }
  .layer {
    height: 830px;
  }
  .monthcontent .monthdata {
    padding: 50px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -25%;
    left: -40%;
  }
  .moreicon .twoicon{
    bottom: -25%;
    left: 50%;
  }
  .modal-dialog {
    width: 50%;
  }
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:568px) {
  .banner-text-w3pvt {
    padding: 35vw 0vw 10vw;
  }
  ul.banner_slide_bg .container-fluid {
    padding: 0;
  }
  .layer {
    height: 755px;
  }
  .monthcontent .monthdata {
    padding: 25px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -25%;
    left: -35%;
  }
  .moreicon .twoicon{
    bottom: -25%;
    left: 35%;
  }
  .modal-dialog {
    width: 50%;
  }
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:480px) {
  h3.heading {
    font-size: 34px;
  }
  #logo img, .footer-title img{
    width: 200px;
  }
  .scrollform{
    top:35%;
  }
  .banner-text-w3pvt {
    padding: 50vw 8vw 8vw;
  }
  #logo a{
    margin-left: 125px;
  }
  h2.heading {
    font-size: 26px;
    line-height: 28px;
  }
  .right-p li,.right-p li a {
    font-size: 14px;
    letter-spacing: 1px;
  }
  .subscribe form {
    width: 100%;
  }
  .copy-right p,.copy-right p a,ul.links a {
    letter-spacing: 1px;
  }

  .textimage {
    margin-left: 0;
  }
  .textimage h1 {
    text-align: center;
    font-size: 27px;
  }
  .textimage h2 {
    text-align: center;
    font-size: 27px;
  }
  .textimage h3 {
    text-align: center;
    font-size: 16px;
  }
  h3.heading, h1.heading{
    font-size: 25px;
  }
  .layer {
    height: 850px;
  }
  .monthcontent .monthdata {
    padding: 25px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -15%;
    left: -40%;
  }
  .moreicon .twoicon{
    bottom: -15%;
    left: 50%;
  }
  .modal-dialog {
    width: 95%;
    left: 10px;
  }
  .popupmodallead form {
    width: 80%;
  }
}
@media (max-width: 425px){
    #logo a{
        margin-left: 100px;
    }

    .monthcontent .monthdata {
    padding: 25px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: 2%;
    left: 0%;
  }
  .moreicon .twoicon{
    bottom: 2%;
    left: 0%;
  }
  .modal-dialog {
    width: 95%;
    left: 10px;
  }
  .popupmodallead form {
    width: 80%;
  }
}
@media(max-width:414px) {
  .navbar-toggler {
    padding: .2rem .7rem;
  }
  #logo img, .footer-title img{
    width: 200px;
  }
  #contactform{
    display: hidden;
  }
  #logo a{
    margin-left: 100px;
  }
  .copy-right p,.copy-right p a,ul.links a {
    font-size: 15px;
  }
  .image {
    width: 20%;
  }
  .copyright {
    padding: 0 1em;
  }
  .copy-right p,.copy-right p a,ul.links a {
    font-size: 14px;
  }
  .footer-title a {
    font-size: 22px;
  }
  h3.heading {
    font-size: 28px;
  }
  nav ul li {
    padding: 5px 0;
  }
  .monthcontent .monthdata {
    padding: 25px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -40%;
    left: -30%;
  }
  .moreicon .twoicon{
    bottom: -40%;
    left: 58%;
  }
  /*.modal-dialog {
    width: 50%;
  }*/
  .popupmodallead form {
    width: 80%;
  }
}

@media(max-width:384px) {
  .right-p li,.right-p li a {
    font-size: 13px;
    letter-spacing: .5px;
  }
  #logo img, .footer-title img{
    width: 200px;
  }
  #logo a{
    margin-left: 71px;
  }
  .banner-text-w3pvt {
    padding: 60vw 7vw 8vw;
    /*padding: 30vw 0vw 8vw;*/
  }
  h2.heading {
    font-size: 21px;
  }
  h3.heading {
    font-size: 25px;
  }
  .footer-text p {
    font-size: 14px;
  }
  .copy-right p,.copy-right p a,ul.links a {
    font-size: 14px;
  }
  .image {
    width: 25%;
  }
  .monthcontent .monthdata {
    padding: 25px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -17%;
    left: 0%;
  }
  .moreicon .twoicon{
    bottom: -40%;
    left: 0%;
  }
  /*.modal-dialog {
    width: 95%;
    left: 10px;
  }*/
  .popupmodallead form {
    width: 80%;
  }
}
@media(max-width:320px) {
   #logo a{
    margin-left: 45px;
  }

  .monthcontent .monthdata {
    padding: 20px;
  }
  .monthcontent .monthdata p{
    font-size: 20px;
  }
  .moreicon .oneicon, .moreicon .twoicon{
    font-size: 35px;
  }
  .moreicon .oneicon p, .moreicon .twoicon p{
    font-size: 18px;
  }
  .moreicon .oneicon{
    bottom: -25%;
    left: 0%;
  }
  .moreicon .twoicon{
    bottom: -55%;
    left: 0%;
  }
  /*.modal-dialog {
    width: 50%;
  }*/
  .popupmodallead form {
    width: 80%;
  }
}
.mb-3, .my-3 {
  color: #fff;
  font-weight: 700;
}
.stickyForm {
   border-radius: 25px;
    position: absolute;
    top: 30%;
    width: 400px;
    right: 50px;
    text-align: center;
    padding: 17px;
    -webkit-transition: .35s ease-in-out;
    -o-transition: .35s ease-in-out;
    transition: .35s ease-in-out;
    z-index: 9999;
}
.stickyForm  p {
  color: #fff;
  font-size: 12px;
  font-weight: 400;
}
.stickyForm.active {
    right: 0px;
    left: auto;
    bottom: auto;
    border-radius: 50px;
    position: fixed;
    top: 20%;
    width: 310px;
    right: -310px;
    text-align: center;
    padding: 17px;
    -webkit-transition: .35s ease-in-out;
    -o-transition: .35s ease-in-out;
    transition: .35s ease-in-out;
    z-index: 9999;
}
.stickyForm, .stickyForm.active .btn {
    background: rgba(0,0,0,0.9);
}
.stickyForm.active .referbtn{
    width: 50%;
    height: 40px;
    padding: 0px;
    left: -100px;
    top: -160px;
    color: #fff;
    background-color: #d60000;
    font-size: 20px;
}
.stickyForm .referbtn {
    position: absolute;
    top: -40px;
    right: auto;
    left: -99px;
    margin: auto;
    height: 50px;
    border-radius: 0px;
    font-size: 15px;
    cursor: pointer;
    border: 0;
    perspective: 100px;
    outline: none;
    width: 60%;
    color: white;
    /*border-radius: 3px 3px 0px 0px !important;*/
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    display: none;
}
.stickyForm ::-webkit-input-placeholder { /* Edge */
  color: #fff;
}
.btn:focus, .btn.focus {
    outline: none;
}
input:focus,
button:focus {
    outline: none;
}
.stickyForm .referbtn {
    top: -220px;
    bottom: -1px;
    margin: auto;
    left: -44px;
    color: #fff;
    height: 30px;
    width: 60px;
    cursor: pointer;
    border-radius: 10px 10px 0 0;
    border-left: 1px solid #d60000;
    border-right: 1px solid #d60000;
    border-top: 1px solid #d60000;
    padding: 1px;
    background: rgba(0,0,0,0.9);
    display: none;
}
.stickyForm span{
  font-size: 12px;
  color: #414141;
}
.stickyForm span a{
  color: #414141;
}
.stickyForm span a:hover{
  text-decoration: underline;
}
body .stickyForm [type="submit"], .popupmodallead [type="submit"] {
    background: #d80514 !important;
    color: #fff;
    height: 40px !important;
    padding: 0px 30px !important;
    border: 0px;
    cursor: pointer;
    border-radius: 15px;
    margin-top: 10px;
}
body .stickyForm label {
    width: 100% !important;
}
body .stickyForm input[type="text"], body .stickyForm input[type="email"], body .stickyForm input[type="tel"], body .stickyForm textarea, body .stickyForm .intl-tel-input {
    width: 100% !important;
    border: 0;
    color: #fff;
    height: 35px;
    background: transparent;
    border-bottom: 1px solid #fff;
    margin-bottom: 13px;
    border-radius: 8px;
}
body .stickyForm label p{
  color: #fff;
  text-align: center;
}
body .stickyForm .radio-inline{
  width: 100% !important;
    border: 0;
    color: #fff;
    height: 35px;
    background: transparent;
    text-align: left;
    display: initial;
    margin-right: 5px;
}
body .stickyForm .radio-inline>input[type=radio] {
  margin-right: 5px;
}
@media only screen and (max-width: 768px){
  .stickyForm {
      position: fixed;
      bottom: -2px;
      top: auto;
      left: auto;
      width: 100%;
      right: 0px;
      text-align: center;
      padding: 17px;
      background: rgba(0,0,0,0.9);
      border-radius: 0px;
      -webkit-transition: .35s ease-in-out;
      -o-transition: .35s ease-in-out;
      transition: .35s ease-in-out;
      z-index: 9999999999;
  }
  .subscribe {width: 100%;}
  .mb-3, .my-3{
    color: #fff;
  }
  .stickyForm ::-webkit-input-placeholder { /* Edge */
      color: #fff;
    }
  .stickyCall {
    position: fixed;
    bottom: 1px;
    top: auto;
    left: 0px;
    width: 50%;
    right: 0px;
    background-color: #d60000;
    height: 40px;
    text-align: center;
    /* padding: 17px; */
    /* background: rgba(0,0,0,0.3); */
    border-radius: 0px;
    -webkit-transition: .35s ease-in-out;
    -o-transition: .35s ease-in-out;
    transition: .35s ease-in-out;
    z-index: 9999;
    border-radius: 5px;
  }
.stickyCall .btnCall{
       width: 100%;
    height: 40px;
    padding: 0px;
    left: 50%;
    cursor: pointer;
    top: -40px;
    background-color: #d60000;
    border:none;
    border-radius:10px;
  }
  .stickyCall a{
    color: #fff;
    font-size:20px;
  }
  .stickyForm.active {
      right: 0px;
      bottom: 0px;
      width: 100%;
      position: fixed;
      bottom: -457px;
      top: auto;
      left: auto;
      width: 100%;
      right: 0px;
      text-align: center;
      padding: 17px;
      background: rgba(0,0,0,0.3);
      border-radius: 0px;
      -webkit-transition: .35s ease-in-out;
      -o-transition: .35s ease-in-out;
      transition: .35s ease-in-out;
      z-index: 9999999999;
  }
  .stickyForm.active .referbtn {
        color: white;
    width: 100%;
    height: 40px;
    padding: 0px;
    left: 0%;
    cursor: pointer;
    top: -130px;
    background-color: #d60000;
}
  .stickyForm .referbtn {
    background: rgba(0,0,0,0.9);
    padding: 1px;
    position: absolute;
    top: -30px;
    left: 0px;
    right: auto;
    margin: auto;
    height: 30px;
    bottom: auto;
    border-radius: 0px;
    font-size: 15px;
    cursor: pointer;
    border: 0;
    perspective: 100px;
    width: 20%;
    color: #fff;
    transform: unset;
    -webkit-transform: unset;
    -moz-transform: unset;
    -o-transform: unset;
    -ms-transform: unset;
    border-radius: 5px 5px 0px 0px;
    display: block;
  }
}
.stickyCall {
    display: none;
}
.btnCTA, .btnCTAOne{
  background-color: #d60000;
    color: #fff;
    padding: 9px;
    border: none;
    border-radius: 10px;
    width: 20%;
    cursor: pointer;
}
.btnCTA:hover, .btnCTAOne:hover{
  /*background-color: #817cce;*/
  /*border: 0.5px solid #000;*/
  color: #000;
}
@media (min-width: 1025px){
    .stickyCall {
        display: none;
    }
}
@media only screen and (max-width: 768px){
    .stickyCall {
        display: block;
    }
    .btnCTA, .btnCTAOne{
      width: 50%;
    }
    .textimage h3{
        line-height:30px;
    }
    .services1 p{
        text-align:center;
        padding:5px;
    }
}