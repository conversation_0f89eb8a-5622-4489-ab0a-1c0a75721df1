
    /*corousel*/
    .premium-carousel .carousel-content {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px; /* Total height of the carousel content */
  }

  .main-image {
      width: 70%; /* Main image width */
      z-index: 2;
      position: relative;
      border-radius: 10px;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      transition: transform 0.5s ease, opacity 0.5s ease;
  }

  .bg-image-left,
  .bg-image-right {
      width: 25%; /* Smaller width for background images */
      height: 50%; /* Smaller height for background images */
      position: absolute;
      top: 50%; /* Center background images vertically */
      transform: translateY(-50%);
      filter:blur(3px);
      opacity: 0.6; /* Reduced opacity for background images */
      z-index: 1; /* Place background images behind the main image */
      transition: transform 0.5s ease, opacity 0.5s ease;
  }

  .bg-image-left {
      left: 5%; /* Position background image on the left */
      transform: translateY(-50%) translateX(-10%);
  }

  .bg-image-right {
      right: 5%; /* Position background image on the right */
      transform: translateY(-50%) translateX(10%);
  }

  /* Transition effects for next/previous slide */
  .carousel-item-next .main-image,
  .carousel-item-prev .main-image {
      transform: scale(0.9);
      opacity: 0.8;
  }

  .carousel-item-next .bg-image-left,
  .carousel-item-prev .bg-image-right {
      transform: translateY(-50%) translateX(0);
      opacity: 1;
  }
  .premium-carousel .carousel-control-prev,
  .premium-carousel .carousel-control-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background-color: rgba(0, 0, 0, 0.5); /* Set background color */
      border-radius: 50%; /* Make it circular */
      width: 50px; /* Width of the circle */
      height: 50px; /* Height of the circle */
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10; /* Ensure the icons are above the carousel */
  }

  .premium-carousel .carousel-control-prev-icon,
  .premium-carousel .carousel-control-next-icon {

      width: 30px; /* Icon size */

      border-radius: 50%; /* Circular icon */
  }

  .premium-carousel .carousel-control-prev {
      left: 10px; /* Position of the previous button */
  }

  .premium-carousel .carousel-control-next {
      right: 10px; /* Position of the next button */
  }




          /*end of crousel*/
          .banner-section {
              position: relative;
              height: 600px;
              background-size: cover;
              background-position: center;
          }
          .banner-cta {
              position: absolute;
              bottom: 50px;
              left: 50%;
              transform: translateX(-50%);
          }
          .premium-carousel .carousel-item img {
              height: 400px;
              object-fit: cover;
          }
          .btn-started {
          background: #2A2B51;
          color : #fff;
          border-color:  #2A2B51;
          }
             :root {
              --h4-font-size-mobile: 1.5rem;
              --h4-font-size-desktop: 1.75rem;
              --h6-font-size-mobile: 1rem;
              --h6-font-size-desktop: 1.25rem;
          }

          /* Headings Styling */
          .section-headings {
              text-align: center;
              margin-bottom: 2rem;
          }

          .section-headings h4 {
              font-size: var(--h4-font-size-mobile);
              font-weight: 700;
              color: #333;
              margin-bottom: 0.5rem;
          }

          .section-headings h6 {
              font-size: var(--h6-font-size-mobile);
              font-weight: 400;
              color: #666;
          }

          /* Card Image Container */
          .card-img-container {
              height: 250px;
              overflow: hidden;
          }

          .card-img-container img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              object-position: center;
          }

          /* Card Body */
          .card-body h6 {
              text-align: left;
              margin-bottom: 0.5rem;
              font-weight: 600;
          }

          .card-body p {
              text-align: left;
              font-size: 0.9rem;
          }

          /* Responsive Adjustments */
          @media (min-width: 768px) {
              .section-headings h4 {
                  font-size: var(--h4-font-size-desktop);
              }

              .section-headings h6 {
                  font-size: var(--h6-font-size-desktop);
              }

              .card-img-container {
                  height: 220px;
              }
          }

          /* Small devices */
          @media (max-width: 767px) {
              .col-md-3 {
                  margin-bottom: 1.5rem;
              }
          }
          .premium-carousel h5 {
      text-align: center; /* Center-align the text */
      margin-bottom: 30px; /* Space below the heading */
      font-size: 1.5rem; /* Adjust font size for better responsiveness */
      font-weight: bold; /* Optional: make the text bold */
  }

  @media (max-width: 768px) {
      .premium-carousel h5 {
          font-size: 1.2rem; /* Adjust font size for smaller screens */
      }
  }


  /*for freqnt question*/
    .custom-btn {
          color: black; /* Default color for questions */
          text-decoration: none; /* Removes underline */
          text-align: left; /* Ensures the text aligns left */
          width: 100%; /* Makes button span full width for alignment */
      }
      .custom-btn:hover {
          text-decoration: none; /* Prevent underline on hover */
      }
      .custom-btn:focus {
          color: black; /* Keeps color black when clicked */
          text-decoration: none; /* Removes focus underline */
          box-shadow: none; /* Removes focus outline shadow */
      }
      .card-header {
          display: flex; /* Use flexbox for alignment */
          align-items: center; /* Vertically align items */
          padding: 10px 15px; /* Add padding for spacing */
      }
      .card-body {
          padding: 15px; /* Consistent padding for answers */
      }

     .modal {
     background: rgba(0, 0, 0, 0.5);
     backdrop-filter: blur(5px);
     }
     .modal-dialog {
     max-width: 400px;
     margin: 1.75rem auto;
     }
     .modal-content {
     border-radius: 12px;
     box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
     border: none;
     }
     .modal-header {
     background-color: #f8f9fa;
     border-top-left-radius: 12px;
     border-top-right-radius: 12px;
     padding: 1rem;
     }
     .modal-title {
     font-weight: 600;
     color: #333;
     }
     .modal-body {
     padding: 1.5rem;
     background-color: white;
     }
     .form-control {
     border-radius: 6px;
     margin-bottom: 1rem;
     padding: 0.75rem;
     }
     .form-control:focus {
     border-color: #007bff;
     box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
     }
     .btn-primary {
     background-color: #007bff;
     border: none;
     border-radius: 6px;
     padding: 0.75rem 1.5rem;
     font-weight: 600;
     transition: background-color 0.3s ease;
     }
     .btn-primary:hover {
     background-color: #0056b3;
     }

   /* ----------------stepwise form css---------------------------- */
   .need-query {
              margin-bottom: 1rem;
          }

          .form-card, .form-card2, .form-card3 {
              padding: 2rem;
              border-radius: 10px;
              background: white;
              max-width: 600px;
              margin: 2rem auto;
            margin-top: -30px;
          }

          .form-title {
              color: black;
              margin-bottom: 2rem;
              text-align: center;
              font-size: 1.2rem !important;
          }

          .form-group {
              position: relative;
              margin-bottom: 1.5rem;
          }

          .form-label {
              position: absolute;
              top: -12px;
              left: 10px;
              background: white;
              padding: 0 5px;
              font-size: 14px;
              color: black;
              transition: all 0.3s ease;
              z-index: 1;
          }

          .form-control, .form-select {
              height: 50px;
              padding: 10px 15px;
              border: 1px solid #ddd;
              border-radius: 5px;
              font-size: 16px;
          }

          .form-control:focus, .form-select:focus {
              border-color: #dc3545;
              box-shadow: none;
          }

          .form-control:focus + .form-label,
          .form-select:focus + .form-label {
              color: #dc3545;
          }

          .btn-next-red {
              background-color: #2A2B51;
              color: white;
              border: none;
              padding: 12px 30px;
              font-size: 16px;
              border-radius: 5px;
              transition: all 0.3s ease;
          }

          .btn-next-red:hover {
              background-color: #595875;
              color: white;
              transform: translateY(-1px);
          }

          /* Radio and Checkbox Styles */
          .radio-group, .checkbox-group {
              margin-top: 25px;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 5px;
              text-align: left;

          }

          .radio-group label, .checkbox-group label {
              display: block;
              margin-bottom: 10px;
              /* padding: 190px !important; */
              cursor: pointer;
              transition: all 0.3s ease;
          }

          .radio-group label:hover, .checkbox-group label:hover {
              background-color: #f8f9fa;
          }

          .radio-group input[type="radio"],
          .checkbox-group input[type="checkbox"] {
              margin-right: 10px;
          }

          /* Button group spacing */
          .button-group {
              margin-top: 2rem;
          }

          /* Back button style */
          .btn-secondary {
              background-color: #6c757d;
              border: none;
              padding: 12px 30px;
              font-size: 16px;
          }

          .btn-secondary:hover {
              background-color: #5a6268;
              transform: translateY(-1px);
          }

          /* Error message style */
          #error-message {
              padding: 10px;
              border-radius: 5px;
              margin-bottom: 1rem;
              font-size: 14px;
          }
          @media only screen and (min-width: 769px) {
          .sections {
            height: 600px !important;
           }
           .content-color {
            margin-top: -160px !important;
           font-size: 27px !important;
          }
           .content-red{
             font-size: 25px !important;
           }
          }

          @media only screen and (max-width: 768px) {
         .content-div, .form-div {
            padding: 10px !important;
            }
            .form-card, .form-card2, .form-card3 {
             padding: 0rem !important;

            }

            .form-title {
            font-size: 1.1rem !important;
            }
            .content-color{
                 font-size: 20px !important;
            }
          .content-red{
             font-size: 17px !important;
           }
           .content-div, .form-div {
                margin-top: -30px !important;
           }
           .content-div1 {
               margin-top: -25px !important;
           }
            .btn-secondary {
                background-color: #6c757d;
                border: none;
                padding: 10px 20px;
                font-size: 16px;
                margin-right: 10px;
            }
            .btn-next-red {
                background-color: #2A2B51;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 16px;
                border-radius: 5px;
                transition: all 0.3s ease;
            }
           .form-label{
              font-size: 11px !important;
           }
           .form-select{
              font-size: 8px !important;
           }
           #backbutton2{
              width: 55px !important;
              height: 45px !important;
              font-size: 13px;
              padding: 0px 5px 0px 5px !important;
           }
           #formnextButton{
              width: 55px !important;
              height: 45px !important;
              font-size: 13px;
              padding: 0px 5px 0px 5px !important;
              border-radius: 5px !important;
           }
           #backbutton3{
              width: 55px !important;
              height: 45px !important;
              font-size: 13px;
              padding: 0px 5px 0px 5px !important;
           }
           .cus_label{
              font-size: 9px !important;
           }
           #filter {
                display:none;
              }
               .cus_heading1{
              padding-top:30px !important;
              padding-bottom:30px !important;
              display:block !important;
              margin: -60px 0px -50px 0px !important;
            }
          }

/* -----------------client testimonial css------------------ */
/* Base Container Styles */
.box-container1 {
  padding: 3rem 0;
}

.title {
  padding-bottom: 1%;
}

.box-container1 .title-detail {
  text-align: center;
  margin-bottom: 2rem;
}

/* Card Styles */
.gray-shedow {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin: 0 auto 20px;
  width: 100%;
  max-width: 350px;
  height: auto;
  min-height: 390px;
}

/* Content Container */
.rep-left {
  position: relative;
  width: 100%;
}

.rep-left img {
  width: 100%;
  height: 200px;
  object-fit: fill;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* Quote Styles */
.big-qoute {
  color: #ccc;
  font-size: 24px;
  margin-right: 10px;
}

.carousel-inner p {
  height: auto;
  min-height: 100px;
  margin: 15px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* Play Button Styles */
.video-btn {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  background-color: #2A2B51;
  font-size: 16px;
  height: 40px;
  width: 40px;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.video-btn:hover {
  background-color: #595875;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Image Overlay */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rep-left:hover .image-overlay {
  opacity: 1;
}

/* Carousel Controls */
#videoCarousel .carousel-control-prev,
#videoCarousel .carousel-control-next {
  width: 40px;
  height: 40px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #f47e45;
  border-radius: 50%;
  opacity: 0.8;
}

#videoCarousel .carousel-control-prev {
  left: -50px;
}

#videoCarousel .carousel-control-next {
  right: -50px;
}

/* Video Modal Styles */
.video-modal .modal-dialog {
  max-width: 800px;
  margin: 30px auto;
  width: 95%;
}

.video-modal .modal-content {
  background-color: transparent;
  border: none;
}

.video-modal .modal-body {
  position: relative;
  padding: 0;
  padding-bottom: 56.25%;
  height: 0;
}

.video-modal .modal-body iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-modal .close {
  position: absolute;
  right: -1px;
  top: -30px;
  color: white;
  font-size: 30px;
  z-index: 999;
  opacity: 1;
  text-shadow: none;
}

.video-modal .close:hover {
  color: #ddd;
}

/* Responsive Breakpoints */
@media (min-width: 1137px) and (max-width: 1440px) {
  .carousel-inner h2,
  .carousel-inner h1 {
      margin-top: 0;
  }
}

@media (min-width: 769px) and (max-width: 1136px) {
  .gray-shedow {
      min-height: 420px;
  }
}

@media (min-width: 512px) and (max-width: 768px) {
  .gray-shedow {
      max-width: 500px;
      min-height: 380px;
  }

  .carousel-inner p {
      min-height: 80px;
  }

  #videoCarousel .carousel-control-prev,
  #videoCarousel .carousel-control-next {
      display: none;
  }
}

@media (max-width: 511px) {
  .box-container1 {
      padding: 1.5rem 0;
  }

  .gray-shedow {
      max-width: 300px;
      min-height: 400px;
  }

  .carousel-inner p {
      min-height: 100px;
  }

  .carousel-inner .btn-one {
      font-size: 14px;
      margin: 10px auto;
      width: 100%;
      max-width: 220px;
      text-align: center;
  }

  .carousel-caption .btn-cta {
      font-size: 14px;
      width: 100%;
      max-width: 200px;
      padding: 8px;
      text-align: center;
  }

  .carousel-caption h5 {
      font-size: 16px !important;
  }

  #videoCarousel .carousel-control-prev,
  #videoCarousel .carousel-control-next {
      display: none;
  }
}

/* -----------contact form css ----------------*/
.support-section .form-label {
  width: 100%;
  background: 0;
  margin-left: -15px !important;
  outline: unset;
 }
.support-section .form-group {
  padding-top: 10px !important;
}
@media only screen and (max-width: 768px) {
  .support-section .form-group {
    padding-top: 17px !important;
  }
}


/* -------price calculator button css --------------*/

.btn:hover {
  color: black !important;
  background-color: #f47e45 !important;
}

.bg-light h6{
    color:black !important;;
}
.card-body p {
    font-size: 0.8rem  !important;
}
/*--------------------storelocator-----------------*/
#storelocator .title{
    background: linear-gradient(to right, #fff7f7, #c9c6c6) !important;
    padding: 10px !important;
     opacity: 0; /* Initially hidden */
    transform: translateY(30px); /* Starts 30px below */
    animation: slideIn 1s ease-out forwards;
}
#storelocator .title:hover{
   color: #f47e45 !important;
}
/*--------design gallery----------*/

@media only screen and (max-width: 768px) {
  #recentproject  a.au-btn--white {
    width: 300px !important;
    padding: 0px !important;
}
}

#recentproject .title{
    background: linear-gradient(to right, #fff7f7, #c9c6c6) !important;
    padding: 10px !important;
     margin-top:20px !important;
    margin-bottom:40px !important;
     opacity: 0; /* Initially hidden */
    transform: translateY(30px); /* Starts 30px below */
    animation: slideIn 1s ease-out forwards;
}
#recentproject .title:hover{
   color: #f47e45 !important;
}
#recentproject .see-more {
        font-size: 24px;
        font-weight: bold;
    }


/*----------testimonials-section-----*/
#testimonials-section .title{
    background: linear-gradient(to right, #fff7f7, #c9c6c6) !important;
    padding: 10px !important;
   opacity: 0; /* Initially hidden */
    transform: translateY(30px); /* Starts 30px below */
    animation: slideIn 1s ease-out forwards;

}
#testimonials-section .title:hover{
   color: #f47e45 !important;
}
@keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }


.support-section h3 {
  color: #f47e45 !important;
}
@media only screen and (max-width: 768px) {

    .mobile-heading {
        display: inline !important;
    }

    .content-div1{
        text-align:center !important;
    }
}
#recentproject a{
    color:white !important;
}
