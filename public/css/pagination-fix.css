/* Pagination Styling Fix */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

/* Override any Bootstrap or other framework styles */
.pagination nav {
    display: flex;
    justify-content: center;
    width: 100%;
}

.pagination .page-item {
    margin: 0 2px;
}

.pagination .page-item .page-link {
    border-radius: 4px;
    padding: 6px 12px;
    color: #333;
    background-color: #fff;
    border: 1px solid #dee2e6;
    font-size: 14px;
    line-height: 1.5;
}

.pagination .page-item.active .page-link {
    background-color: #f47e45;
    border-color: #f47e45;
    color: #fff;
}

.pagination .page-item .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #333;
}

/* Fix for pagination arrows */
.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
    font-size: 14px !important;
    padding: 6px 12px !important;
    line-height: 1.5 !important;
    width: auto !important;
    height: auto !important;
    max-width: 38px !important;
    max-height: 38px !important;
}

/* Fix for arrow icons */
.pagination .page-item:first-child .page-link span,
.pagination .page-item:last-child .page-link span,
.pagination .page-item:first-child .page-link svg,
.pagination .page-item:last-child .page-link svg {
    font-size: 14px !important;
    line-height: 1.5 !important;
    width: 14px !important;
    height: 14px !important;
    max-width: 14px !important;
    max-height: 14px !important;
}

/* Ensure consistent height for all pagination elements */
.pagination .page-link {
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 5px 10px;
        font-size: 13px;
        height: 34px;
    }

    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link {
        max-width: 34px !important;
        max-height: 34px !important;
    }
}

/* Specific fix for portfolio index page */
.card-body .d-flex.justify-content-center.mt-4 .pagination {
    margin-top: 0;
}

.card-body .d-flex.justify-content-center.mt-4 .pagination .page-item:first-child .page-link,
.card-body .d-flex.justify-content-center.mt-4 .pagination .page-item:last-child .page-link {
    font-size: 14px !important;
    padding: 6px 12px !important;
    line-height: 1.5 !important;
    width: auto !important;
    height: auto !important;
    max-width: 38px !important;
    max-height: 38px !important;
}

.card-body .d-flex.justify-content-center.mt-4 .pagination .page-item:first-child .page-link svg,
.card-body .d-flex.justify-content-center.mt-4 .pagination .page-item:last-child .page-link svg {
    width: 14px !important;
    height: 14px !important;
    max-width: 14px !important;
    max-height: 14px !important;
}
