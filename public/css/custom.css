.close:focus {
    border:none;
}
@media(min-width:320px) and (max-width:512px) {
    .modal-content{
        font-size:12px;
   }
    .modal-content h5{
        margin-left:10px !important;
        font-size:15px !important;
   }
    .modal-content .close{
        font-size:30px !important;
   }
}
.customwidth {
    min-width: 250px !important;
    text-align: left !important;
    padding :15px !important;
}
.menu-list {
    list-style: none;
}
/* Base styles for smooth transitions */
.service__item {
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    height: 300px;
   /* Adjust height as needed */
}
/* Background image effect */
.service__item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-position: center;
    background-size: cover;
    transition: transform 0.3s ease;
    z-index: 1;
    opacity: 0.5;
}
/* Centering the inner content */
.service__item-inner {
    position: absolute;
    top: 50%;
   /* Moves to center */
    left: 50%;
   /* Moves to center */
    transform: translate(-50%, -50%);
   /* Adjusts to center perfectly */
    z-index: 3;
    text-align: center;
    padding: 10px 20px;
    border-radius: 5px;
   /* Optional: adds rounded corners */
    margin-top:70px;
}
/* Heading styles */
.service__item h2 {
    margin: 0;
    padding: 10px 0;
}
.service__item h2 a {
    display: inline-block;
    transition: all 0.3s ease;
    color: white;
   /* Default text color */
    font-size: 25px;
    font-weight: bolder;
}
/* Hover effects */
.service__item:hover::after {
    transform: scale(1.2);
    opacity: 1;
}
.service__item:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.62);
    z-index: 2;
}
.service__item:hover h2 a {
    color: #f47e45 !important;
    transform: scale(1.2);
    font-size: 28px !important;
    font-weight: 1000 !important;
}
body{
    overflow-x:hidden;
    width:100%;
}
.services-grid {
    display: grid;
   /*grid-gap: 1rem;
   */
    justify-content: center;
}
.service-item {
    text-align: center;
    margin-bottom: 0.5rem;
}
.icon-size {
    width: 80px;
   /* Adjust as needed */
    height: auto;
   /* Maintain aspect ratio */
}
.icon-size1 {
    width: 80px;
   /* Adjust as needed */
    height: auto;
   /* Maintain aspect ratio */
}
/* Adjust grid layout for larger screens */
@media screen and (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(5, 1fr);
   }
}
/* Adjust grid layout for smaller screens */
@media screen and (max-width: 767px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
   }
}
/* -----------------client testimonial css------------------ */
/* Base Container Styles */
.box-container1 {
    padding: 3rem 0;
}
.title {
    padding-bottom: 1%;
}
.box-container1 .title-detail {
    text-align: center;
    margin-bottom: 2rem;
}
/* Card Styles */
.gray-shedow {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 0 auto 20px;
    width: 100%;
    max-width: 350px;
    height: auto;
    min-height: 390px;
}
/* Content Container */
.rep-left {
    position: relative;
    width: 100%;
}
.rep-left img {
    width: 100%;
    height: 200px;
    object-fit: fill;
    border-radius: 4px;
    margin-bottom: 15px;
}
/* Quote Styles */
.big-qoute {
    color: #ccc;
    font-size: 24px;
    margin-right: 10px;
}
.carousel-inner p {
    height: auto;
    min-height: 100px;
    overflow: visible;
    display: block;
}
/* Play Button Styles */
.video-btn {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    background-color: #f47e45;
    font-size: 16px;
    height: 40px;
    width: 40px;
    border: none;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}
.video-btn:hover {
    background-color: #e06835;
    transform: translate(-50%, -50%) scale(1.1);
}
/* Image Overlay */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.rep-left:hover .image-overlay {
    opacity: 1;
}
/* Carousel Controls */
#videoCarousel .carousel-control-prev, #videoCarousel .carousel-control-next {
    width: 40px;
    height: 40px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #f47e45;
    border-radius: 50%;
    opacity: 0.8;
}
#videoCarousel .carousel-control-prev {
    left: -50px;
}
#videoCarousel .carousel-control-next {
    right: -50px;
}
/* Video Modal Styles */
.video-modal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
    width: 95%;
}
.video-modal .modal-content {
    background-color: transparent;
    border: none;
}
.video-modal .modal-body {
    position: relative;
    padding: 0;
    padding-bottom: 56.25%;
    height: 0;
}
.video-modal .modal-body iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.video-modal .close {
    position: absolute;
    right: -1px;
    top: -30px;
    color: white;
    font-size: 30px;
    z-index: 999;
    opacity: 1;
    text-shadow: none;
}
.video-modal .close:hover {
    color: #ddd;
}
/* Responsive Breakpoints */
@media (min-width: 1137px) and (max-width: 1440px) {
    .carousel-inner h2, .carousel-inner h1 {
        margin-top: 0;
   }
}
@media (min-width: 769px) and (max-width: 1136px) {
    .gray-shedow {
        min-height: 420px;
   }
}
@media (min-width: 512px) and (max-width: 768px) {
    .gray-shedow {
        max-width: 500px;
        min-height: 380px;
   }
    .carousel-inner p {
        min-height: 80px;
   }
    #videoCarousel .carousel-control-prev, #videoCarousel .carousel-control-next {
        display: none;
   }
}
@media (max-width: 511px) {
    .box-container1 {
        padding: 1.5rem 0;
   }
    .gray-shedow {
        max-width: 300px;
        min-height: 400px;
   }
    .carousel-inner p {
        min-height: 100px;
   }
    .carousel-inner .btn-one {
        font-size: 14px;
        margin: 10px auto;
        width: 100%;
        max-width: 220px;
        text-align: center;
   }
    .carousel-caption .btn-cta {
        font-size: 14px;
        width: 100%;
        max-width: 200px;
        padding: 8px;
        text-align: center;
   }
    .carousel-caption h5 {
        font-size: 16px !important;
   }
    #videoCarousel .carousel-control-prev, #videoCarousel .carousel-control-next {
        display: none;
   }
}
@media only screen and (max-width: 512px) {
    .carousel-inner h1{
        margin-top:50px;
   }
    .carousel-inner .btn-one{
        font-size:12px !important;
        margin-left:10px;
        width:220px;
        text-align:center;
   }
    .carousel-caption .btn-cta{
        font-size:12px !important;
        width:200px;
        padding:5px;
        text-align:center;
   }
    .carousel-caption h5{
        font-size:15px !important;
   }
    a.au-btn--white {
        font-size: 14px;
   }
   .breadlist{
    margin-top:60px !important;
  }
}
@media (min-width: 1137px) and (max-width: 1440px) {
    .carousel-inner h2{
        margin-top:-250px;
   }
    .carousel-inner h1{
        margin-top:-250px;
   }
}
.table-container {
   /* Apply Flexbox*/
    display: flex;
   /* Horizontally center the Table*/
    justify-content: center;
   /* Verticalyy center the Table*/
    align-items: center;
   /* TO display the container box*/
   /*height: 300px;
   */
    width: 100%;
    padding: 20px;
   /*border: 1px solid black;
   */
}
table, th, td {
    border: 1px solid black;
    padding: 10px;
}
@media only screen and (max-width: 320px) {
    table, th, td {
        font-size:12px;
   }
}


/* Estimate by service type pop up */
.service-type-option {
    display: block;
    position: relative;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
    text-decoration: none;
    color: #fff !important;
    text-align: center;
    transition: transform 0.3s ease;
}

.service-type-option:hover {
    transform: scale(1.05);
    text-decoration: none;
    color: #fff;
}

.interior-option {
    background: url('/images/home-villa/5Living-Room.jpg') center center/cover no-repeat;
}

.construction-option {
    background: url('/images/home-villa/2Kitchen-HsrLayout.jpg') center center/cover no-repeat;
}

.service-type-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.service-type-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: bold;
    z-index: 2;
    width: 100%;
}

.page_top_banner{
    max-height: 500px;
}