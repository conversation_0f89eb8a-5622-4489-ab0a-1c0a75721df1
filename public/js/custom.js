function openModal() {
    $('#contactModal').modal('show');
}

  document.addEventListener("DOMContentLoaded", function () {
    let lazyItems = document.querySelectorAll(".carousel-item");

    let observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                let item = entry.target;
                let bgImage = item.getAttribute("data-bg");
                if (bgImage) {
                    item.style.backgroundImage = `url('${bgImage}')`;
                    item.classList.add("loaded"); // Prevents reloading
                }
                observer.unobserve(item);
            }
        });
    }, { rootMargin: "200px" });

    lazyItems.forEach((item) => observer.observe(item));
});

$(document).ready(function () {
    // Select the carousel element
    $('#carouselExampleCaptions').carousel({
        interval: 5000, // Enable auto-sliding with 5 second interval
        ride: 'carousel' // Enable automatic start
    });
});


$(document).ready(function() {
    // Handle video thumbnail clicks
    $('.video-link').click(function(e) {
        e.preventDefault();
        var videoId = $(this).data('video');
        var iframe = $('#videoModal iframe');
        iframe.attr('src', 'https://www.youtube.com/embed/' + videoId + '?autoplay=1');
        $('#videoModal').modal('show');
    });

    // Clear iframe src when modal is closed
    $('#videoModal').on('hidden.bs.modal', function() {
        $('#videoModal iframe').attr('src', '');
    });
});