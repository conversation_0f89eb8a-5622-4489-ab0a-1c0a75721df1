(function($){"use strict";var $topeContainer=$('#isotope-grid');var $filter=$('#filter');$filter.each(function(){$filter.on('click','li span',function(){var filterValue=$(this).attr('data-filter');if(filterValue=="*"){$topeContainer.isotope({filter:':not(.In-Progress)' + filterValue});}else{$topeContainer.isotope({filter:filterValue});}});var $buttonGroup=$('#filter');$buttonGroup.on('click','li',function(){$buttonGroup.find('.active').removeClass('active');$(this).addClass('active');});});$(window).on('load',function(){var $grid=$topeContainer.each(function(){$(this).isotope({filter:':not(.In-Progress)',itemSelector:'.item',percentPosition:true,animationEngine:'best-available',masonry:{columnWidth:'.item'}});});});})(jQuery);