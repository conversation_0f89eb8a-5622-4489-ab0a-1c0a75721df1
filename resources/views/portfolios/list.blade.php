@extends('layouts.masterlayout')

@section('content')
    {{-- Top Banner Section --}}
    <section class="add-background">
        <div class="row">
            <div class="col-md-12">
                <img src="images/breadcumb/Portfolio-desk.jpg" alt="Best Living Room interior designers in Gurugram"  class="choose-1" width="100%;" loading="lazy">
            </div>
            <div class="col-md-12">
                <img src="images/breadcumb/Portfolio-Mob.jpg" alt="Top Kitchen Interior designers in Gurugram" class="choose-2" width="100%;" loading="lazy">
            </div>
            <div class="pos-resize" style="margin-top:-40px">
                <h1>OUR PORTFOLIO</h1>
            </div>
            <div class="breadlist" style="margin-top:30px">
                <ul class="list-inline">
                    <li><a href="/">Home</a></li>
                    <li>/</li>
                    <li class="active">Interior Design Portfolio</li>
                </ul>
            </div>
        </div>
    </section>

    <section class="project1">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h2 style="color:#C7A45F;display:none;" class="cus_heading">Our Design Gallery</h2>
                    <div id="filter-wrap">
                        <ul id="filter" class="ul--no-style ul--inline">
                            <li class="active">
                                <span data-filter="*">All</span>
                            </li>
                            <li>
                                <span data-filter=".BedRoom">BedRoom</span>
                            </li>
                            <li>
                                <span data-filter=".ModularKitchen">Modular Kitchen</span>
                            </li>
                            <li>
                                <span data-filter=".LivingRoom">Living Room</span>
                            </li>
                            <li>
                                <span data-filter=".Office">Office</span>
                            </li>
                            <li>
                                <span data-filter=".Construction">Construction</span>
                            </li>
                            <li>
                                <span data-filter=".In-Progress">In-Progress</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div id="isotope-grid" class="row no-gutters">
                @forelse($portfolios as $portfolio)
                <div class="col-lg-3 col-md-6 item {{ $portfolio->project_type_class }}">
                    <div class="latest__item">
                        <img alt="{{ $portfolio->title }}" src="{{ asset('images/portfolios/' . $portfolio->featured_image) }}" width="100%;">
                        <div class="overlay overlay--invisible overlay--p-15">
                            <div class="overlay--border">
                            </div>
                        </div>
                        <div class="latest__item--content">
                            <div class="latest__item--inner">
                                <h3>
                                    <a href="portfolios/{{ $portfolio->seo_url }}">
                                    {{ $portfolio->title }}</a>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-md-12 text-center">
                    <p>No portfolios found.</p>
                </div>
                @endforelse

            </div>
        </div>
    </section>

    {{-- Enquire Online Section --}}
    @include('includes.enquire_online_section')
@endsection
@section('onPageJS')
<script src="{{config('app.url')}}/js/isotope.pkgd.min.js"></script>
<script src="{{config('app.url')}}/js/isotope-custom.js"></script>
@endsection