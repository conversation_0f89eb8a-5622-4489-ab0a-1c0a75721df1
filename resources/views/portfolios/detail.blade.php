@extends('layouts.masterlayout')
@section('content')
<section class="dual-container">
    <div class="container">
        <div class="row">
            <div class="col-md-7">
                <img alt="{{ $portfolio->title }}" src="{{ asset('images/portfolios/' . $portfolio->featured_image) }}" class="img-responsive">
            </div>
            <div class="col-md-5">
                <h1>{{ $portfolio->title }}</h1>
                {!! $portfolio->description !!}
            </div>
        </div>
    </div>
</section>
<section class="latest-project Project-test" id="recentproject">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-6 col-12">
                <h2 class="title">{{ $portfolio->title }}</h2>
                <p class="title-detail">{{ $portfolio->category ? ucfirst($portfolio->category->display_name) : '' }}</p>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            @forelse($portfolio->images_array as $imagePath)
            <div class="col-md-3 project-otherimg">
                <a class="fancybox" data-fancybox="images" href="{{ asset('images/portfolios/' . $imagePath) }}">
                    <img src="{{ asset('images/portfolios/' . $imagePath) }}" alt="{{ $portfolio->title }}" />
                </a>
            </div>
            @empty
            <div class="col-md-12 text-center">
                <p>No additional images available.</p>
            </div>
            @endforelse
        </div>
    </div>
</section>
<section class="project-container">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h3>Other <span>projects</span></h3>
            </div>
        </div>
        <div class="row">
            @forelse($otherPortfolios as $otherPortfolio)
            <div class="col-md-3 project-container-1">
                <img src="{{ asset('images/portfolios/' . $otherPortfolio->featured_image) }}" alt="{{ $otherPortfolio->title }}" width="270px" height="146px">
                <div class="box-1">
                    <h4><a href="{{ $otherPortfolio->seo_url }}">{{ $otherPortfolio->title }}</a></h4>
                </div>
            </div>
            @empty
            <div class="col-md-12 text-center">
                <p>No other projects available.</p>
            </div>
            @endforelse
        </div>
    </div>
</section>
@endsection