@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('View Portfolio') }}</span>
                    <div>
                        <a href="{{ route('portfolio.edit', $portfolio->id) }}" class="btn btn-primary btn-sm">Edit</a>
                        <a href="{{ route('portfolio.index') }}" class="btn btn-secondary btn-sm">Back to List</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 200px;">ID</th>
                                    <td>{{ $portfolio->id }}</td>
                                </tr>
                                <tr>
                                    <th>Title</th>
                                    <td>{{ $portfolio->title }}</td>
                                </tr>
                                <tr>
                                    <th>Project Type</th>
                                    <td>{{ ucfirst($portfolio->project_type) }}</td>
                                </tr>
                                <tr>
                                    <th>SEO URL</th>
                                    <td>{{ $portfolio->seo_url }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge {{ $portfolio->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                            {{ ucfirst($portfolio->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $portfolio->created_at->format('F d, Y h:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $portfolio->updated_at->format('F d, Y h:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4">
                            @if($portfolio->featured_image)
                                <div class="card">
                                    <div class="card-header">Featured Image</div>
                                    <div class="card-body text-center">
                                        <img src="{{ asset('images/portfolios/' . $portfolio->featured_image) }}" alt="{{ $portfolio->title }}" class="img-fluid">
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Description</div>
                                <div class="card-body">
                                    {!! $portfolio->description !!}
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(!empty($portfolio->images_array))
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">Portfolio Images</div>
                                    <div class="card-body">
                                        <div class="row">
                                            @foreach($portfolio->images_array as $imagePath)
                                                <div class="col-md-3 mb-3">
                                                    <img src="{{ asset('images/portfolios/' . $imagePath) }}" alt="Portfolio Image" class="img-fluid">
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
