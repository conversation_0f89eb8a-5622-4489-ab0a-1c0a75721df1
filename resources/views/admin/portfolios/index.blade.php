@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Portfolio Management') }}</span>
                    <a href="{{ route('portfolio.create') }}" class="btn btn-primary btn-sm">Add New Portfolio</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Project Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($portfolios as $portfolio)
                                    <tr>
                                        <td>{{ $portfolio->id }}</td>
                                        <td>
                                            @if($portfolio->featured_image)
                                                <img src="{{ asset('images/portfolios/' . $portfolio->featured_image) }}" alt="{{ $portfolio->title }}" style="max-width: 100px; max-height: 60px;">
                                            @else
                                                No Image
                                            @endif
                                        </td>
                                        <td>{{ $portfolio->title }}</td>
                                        <td>
                                            @if($portfolio->category)
                                                <span class="badge bg-secondary">{{ $portfolio->category->display_name }}</span>
                                            @else
                                                <span class="text-muted">None</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $portfolio->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                                {{ ucfirst($portfolio->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('portfolio.show', $portfolio->id) }}" class="btn btn-info btn-sm">View</a>
                                                <a href="{{ route('portfolio.edit', $portfolio->id) }}" class="btn btn-primary btn-sm">Edit</a>
                                                <form action="{{ route('portfolio.destroy', $portfolio->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this portfolio? This will also delete all associated images.');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No portfolios found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center">
                        {{ $portfolios->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
