@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('Dashboard') }}</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Services Management</h5>
                                </div>
                                <div class="card-body">
                                    <p>Manage your services, categories, and service details.</p>
                                    <a href="{{ route('service.index') }}" class="btn btn-primary">Manage Services</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Portfolio Management</h5>
                                </div>
                                <div class="card-body">
                                    <p>Manage your portfolio projects and project images.</p>
                                    <a href="{{ route('portfolio.index') }}" class="btn btn-success">Manage Portfolio</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Categories Management</h5>
                                </div>
                                <div class="card-body">
                                    <p>Manage service and portfolio categories for better organization.</p>
                                    <a href="{{ route('category.index') }}" class="btn btn-secondary">Manage Categories</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-warning text-white">
                                    <h5 class="mb-0">City Management</h5>
                                </div>
                                <div class="card-body">
                                    <p>Manage cities and their banner images for interior design services.</p>
                                    <a href="{{ route('city.index') }}" class="btn btn-warning">Manage Cities</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Website Preview</h5>
                                </div>
                                <div class="card-body">
                                    <p>View your website as visitors will see it.</p>
                                    <a href="{{ url('/') }}" class="btn btn-info" target="_blank">View Website</a>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
@endsection
