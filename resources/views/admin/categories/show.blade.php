@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Category Details</h4>
                    <div>
                        <a href="{{ route('category.edit', $category->id) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ route('category.index') }}" class="btn btn-secondary">Back to List</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 200px;">ID</th>
                                    <td>{{ $category->id }}</td>
                                </tr>
                                <tr>
                                    <th>Name (Internal)</th>
                                    <td><code>{{ $category->name }}</code></td>
                                </tr>
                                <tr>
                                    <th>Display Name</th>
                                    <td>{{ $category->display_name }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge {{ $category->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                            {{ ucfirst($category->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Services Count</th>
                                    <td>{{ $category->services()->count() }}</td>
                                </tr>
                                <tr>
                                    <th>Portfolios Count</th>
                                    <td>{{ $category->portfolios()->count() }}</td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $category->created_at->format('F d, Y h:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $category->updated_at->format('F d, Y h:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($category->services()->count() > 0)
                        <div class="mt-4">
                            <h5>Services using this category:</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($category->services()->take(10)->get() as $service)
                                            <tr>
                                                <td>{{ $service->id }}</td>
                                                <td>{{ $service->title }}</td>
                                                <td>
                                                    <span class="badge {{ $service->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                                        {{ ucfirst($service->status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('service.show', $service->id) }}" class="btn btn-sm btn-info">View</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif

                    @if($category->portfolios()->count() > 0)
                        <div class="mt-4">
                            <h5>Portfolios using this category:</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($category->portfolios()->take(10)->get() as $portfolio)
                                            <tr>
                                                <td>{{ $portfolio->id }}</td>
                                                <td>{{ $portfolio->title }}</td>
                                                <td>
                                                    <span class="badge {{ $portfolio->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                                        {{ ucfirst($portfolio->status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('portfolio.show', $portfolio->id) }}" class="btn btn-sm btn-info">View</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
