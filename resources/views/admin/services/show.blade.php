@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('View Service') }}</span>
                    <div>
                        <a href="{{ route('service.edit', $service->id) }}" class="btn btn-primary btn-sm">Edit</a>
                        <a href="{{ route('service.index') }}" class="btn btn-secondary btn-sm">Back to List</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 200px;">ID</th>
                                    <td>{{ $service->id }}</td>
                                </tr>
                                <tr>
                                    <th>Title</th>
                                    <td>{{ $service->title }}</td>
                                </tr>
                                <tr>
                                    <th>Category</th>
                                    <td>
                                        @if($service->category)
                                            {{ $service->category->display_name }} <small class="text-muted">({{ $service->category->name }})</small>
                                        @else
                                            <span class="text-muted">None</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>SEO Title</th>
                                    <td>{{ $service->seo_title }}</td>
                                </tr>
                                <tr>
                                    <th>SEO URL</th>
                                    <td>{{ $service->seo_url }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge {{ $service->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                            {{ ucfirst($service->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Service Type</th>
                                    <td>
                                        @if($service->is_interior)
                                            <span class="badge bg-primary me-1">Interior</span>
                                        @endif
                                        @if($service->is_construction)
                                            <span class="badge bg-info me-1">Construction</span>
                                        @endif
                                        @if($service->is_other)
                                            <span class="badge bg-secondary me-1">Other</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $service->created_at->format('F d, Y h:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $service->updated_at->format('F d, Y h:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4">
                            @if($service->top_banner_image)
                                <div class="card">
                                    <div class="card-header">Banner Image</div>
                                    <div class="card-body text-center">
                                        <img src="{{ asset('images/services/' . $service->top_banner_image) }}" alt="{{ $service->title }}" class="img-fluid">
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Description</div>
                                <div class="card-body">
                                    {!! $service->description !!}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Service Read More</div>
                                <div class="card-body">
                                    {!! $service->service_read_more !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
