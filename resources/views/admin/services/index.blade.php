@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Services Management') }}</span>
                    <a href="{{ route('service.create') }}" class="btn btn-primary btn-sm">Add New Service</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Parent</th>
                                    <th>Menu Order</th>
                                    <th>In Menu</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($services as $service)
                                    <tr>
                                        <td>{{ $service->id }}</td>
                                        <td>
                                            @if($service->parent_id)
                                                <span class="ms-3">└─ </span>
                                            @endif
                                            {{ $service->title }}
                                            @if($service->hasChildren())
                                                <span class="badge bg-info">{{ $service->children()->count() }} children</span>
                                            @endif
                                        </td>
                                        <td>{{ $service->parent ? $service->parent->title : 'None' }}</td>
                                        <td>{{ $service->menu_order }}</td>
                                        <td>
                                            <span class="badge {{ $service->show_in_menu ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $service->show_in_menu ? 'Yes' : 'No' }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($service->is_interior)
                                                <span class="badge bg-primary me-1">I</span>
                                            @endif
                                            @if($service->is_construction)
                                                <span class="badge bg-info me-1">C</span>
                                            @endif
                                            @if($service->is_other)
                                                <span class="badge bg-secondary me-1">O</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $service->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                                {{ ucfirst($service->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('service.show', $service->id) }}" class="btn btn-info btn-sm">View</a>
                                                <a href="{{ route('service.edit', $service->id) }}" class="btn btn-primary btn-sm">Edit</a>
                                                <form action="{{ route('service.destroy', $service->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this service?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">No services found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $services->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
