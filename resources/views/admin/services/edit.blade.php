@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Edit Service') }}</span>
                    <a href="{{ route('service.index') }}" class="btn btn-secondary btn-sm">Back to List</a>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('service.update', $service->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $service->title) }}" required>
                                    @error('title')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">Category <span class="category-required {{ $service->parent_id ? '' : 'd-none' }} text-danger">*</span></label>
                                    <input type="text" class="form-control @error('category') is-invalid @enderror" id="category" name="category" value="{{ old('category', $service->category) }}" {{ $service->parent_id ? 'required' : '' }}>
                                    <small class="form-text text-muted">Required only for sub-services (when parent is selected).</small>
                                    @error('category')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="seo_title">SEO Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('seo_title') is-invalid @enderror" id="seo_title" name="seo_title" value="{{ old('seo_title', $service->seo_title) }}" required>
                                    @error('seo_title')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="seo_url">SEO URL <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('seo_url') is-invalid @enderror" id="seo_url" name="seo_url" value="{{ old('seo_url', $service->seo_url) }}" required>
                                    @error('seo_url')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5" required>{{ old('description', $service->description) }}</textarea>
                            @error('description')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="service_read_more">Service Read More <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('service_read_more') is-invalid @enderror" id="service_read_more" name="service_read_more" rows="5" required>{{ old('service_read_more', $service->service_read_more) }}</textarea>
                            @error('service_read_more')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="parent_id">Parent Service</label>
                                    <select class="form-control @error('parent_id') is-invalid @enderror" id="parent_id" name="parent_id">
                                        <option value="">None (Top Level)</option>
                                        @foreach(\App\Models\Service::whereNull('parent_id')->where('id', '!=', $service->id)->orderBy('title')->get() as $parentService)
                                            <option value="{{ $parentService->id }}" {{ old('parent_id', $service->parent_id) == $parentService->id ? 'selected' : '' }}>{{ $parentService->title }}</option>
                                            @foreach($parentService->children()->where('id', '!=', $service->id)->get() as $childService)
                                                <option value="{{ $childService->id }}" {{ old('parent_id', $service->parent_id) == $childService->id ? 'selected' : '' }}>&nbsp;&nbsp;&nbsp;-- {{ $childService->title }}</option>
                                            @endforeach
                                        @endforeach
                                    </select>
                                    <small class="form-text text-muted">A service cannot be its own parent or child.</small>
                                    @error('parent_id')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="menu_order">Menu Order</label>
                                    <input type="number" class="form-control @error('menu_order') is-invalid @enderror" id="menu_order" name="menu_order" value="{{ old('menu_order', $service->menu_order) }}">
                                    <small class="form-text text-muted">Lower numbers appear first in the menu.</small>
                                    @error('menu_order')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="top_banner_image">Top Banner Image</label>
                                    <input type="file" class="form-control @error('top_banner_image') is-invalid @enderror" id="top_banner_image" name="top_banner_image">
                                    <small class="form-text text-muted">Max size: 200KB. Allowed formats: JPG, JPEG, PNG. Leave empty to keep the current image.</small>
                                    @error('top_banner_image')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror

                                    @if($service->top_banner_image)
                                        <div class="mt-2">
                                            <label>Current Image:</label>
                                            <div>
                                                <img src="{{ asset('images/services/' . $service->top_banner_image) }}" alt="{{ $service->title }}" style="max-width: 200px; max-height: 100px;">
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Status <span class="text-danger">*</span></label>
                                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="active" {{ old('status', $service->status) == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status', $service->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="show_in_menu" id="show_in_menu" value="1" {{ old('show_in_menu', $service->show_in_menu) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_in_menu">
                                    Show in Menu
                                </label>
                                <small class="form-text text-muted">Uncheck this if you don't want this service to appear in the navigation menu.</small>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label>Service Type</label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_interior" id="is_interior" value="1" {{ old('is_interior', $service->is_interior ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_interior">
                                        Interior
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_construction" id="is_construction" value="1" {{ old('is_construction', $service->is_construction ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_construction">
                                        Construction
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_other" id="is_other" value="1" {{ old('is_other', $service->is_other ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_other">
                                        Other
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Service</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('onPageJS')
<script src="https://cdn.ckeditor.com/4.16.2/standard/ckeditor.js"></script>
<script>
    CKEDITOR.replace('description');
    CKEDITOR.replace('service_read_more');

    // Toggle category required state based on parent selection
    document.addEventListener('DOMContentLoaded', function() {
        const parentSelect = document.getElementById('parent_id');
        const categoryInput = document.getElementById('category');
        const categoryRequiredSpan = document.querySelector('.category-required');

        function toggleCategoryRequired() {
            if (parentSelect.value) {
                categoryInput.setAttribute('required', 'required');
                categoryRequiredSpan.classList.remove('d-none');
            } else {
                categoryInput.removeAttribute('required');
                categoryRequiredSpan.classList.add('d-none');
            }
        }

        // Initial state
        toggleCategoryRequired();

        // Listen for changes
        parentSelect.addEventListener('change', toggleCategoryRequired);
    });
</script>
@endsection
