@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('View City') }}</span>
                    <div>
                        <a href="{{ route('city.edit', $city->id) }}" class="btn btn-primary btn-sm">Edit</a>
                        <a href="{{ route('city.index') }}" class="btn btn-secondary btn-sm">Back to List</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 200px;">ID</th>
                                    <td>{{ $city->id }}</td>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <td>{{ $city->name }}</td>
                                </tr>
                                <tr>
                                    <th>SEO URL</th>
                                    <td>{{ $city->seo_url }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge {{ $city->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                            {{ ucfirst($city->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $city->created_at->format('F d, Y h:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $city->updated_at->format('F d, Y h:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            @if($city->banner)
                                <div class="card">
                                    <div class="card-header">Banner Image</div>
                                    <div class="card-body text-center">
                                        <img src="{{ asset('images/cities/' . $city->banner) }}" alt="{{ $city->name }}" class="img-fluid">
                                    </div>
                                </div>
                            @else
                                <div class="card">
                                    <div class="card-header">Banner Image</div>
                                    <div class="card-body text-center">
                                        <p>No banner image available.</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('interior.cities.detail', $city->seo_url) }}" target="_blank" class="btn btn-info">
                            View City Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
