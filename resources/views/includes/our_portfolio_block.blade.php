<section class="latest-project" id="recentproject">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-md-8 col-12">
                <h3 class="title">Our {{$city->name ?? 'Gurugram'}} Interior Designs Portfolio</h3>
                <p align="justify" class="title-detail">Elevating Ideas Into Lasting Spaces - Dive Into Our Portfolio to Know More</p>
            </div>
        </div>
    </div>
    <div class="row no-gutters">
        @if(isset($featuredPortfolios) && $featuredPortfolios->count() > 0)
            @php
                // Split portfolios into 4 columns
                $portfoliosChunks = $featuredPortfolios->chunk(ceil($featuredPortfolios->count() / 4));
            @endphp

            @foreach($portfoliosChunks as $chunk)
                <div class="col-lg-3 col-md-6">
                    @foreach($chunk as $portfolio)
                        <div class="latest__item">
                            <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="
                                data-src="{{ asset('images/portfolios/' . $portfolio->featured_image) }}"
                                alt="{{ $portfolio->title }}"
                                loading="lazy">
                            <div class="overlay overlay--invisible overlay--p-15">
                                <div class="overlay--border"></div>
                            </div>
                            <div class="latest__item--content">
                                <div class="latest__item--inner">
                                    <h3><a href="{{ url('portfolios/' . $portfolio->seo_url) }}">{{ $portfolio->title }}</a></h3>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endforeach
        @else
            <div class="col-12 text-center">
                <p>No portfolio items available at the moment.</p>
            </div>
        @endif
    </div>
    <div class="col-md-12 text-center">
        <div class="see-more">
            <a href="{{ url('portfolios') }}" class="au-btn au-btn--big au-btn--pill au-btn--yellow au-btn--white">View Our Complete Portfolio</a>
        </div>
    </div>
</section>