<style>
  .copyright .set{
  margin-right:-15px !important;
  }
</style>
<footer>
  <div class="parallax parallax--footer">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-4 col-md-4 col-12">
          <a href="/"><img alt="{{config('app.name')}}" width="1600" height="900" src="{{asset('images/logo_footer.png')}}"></a>
          <p class="about; text-align:justify silka-regular">
            Founded in 2025, {{config('app.name')}} has quickly become a leading and beloved home interior brand, committed to delivering premium, end-to-end luxury interiors at fair prices. With over {{config('app.projects_delivered')}} beautifully completed homes and a talented team of {{config('app.team_size')}}+ design experts, we bring dream home visions to life across {{$cities->count()}} cities, including
            @forelse ($cities as $city)
              {{$city->name.', '}}
            @empty

            @endforelse
          </p>
          <ul class="list-style-type:none; text-left set">
            @forelse($topServices as $service)
              <li>
                <a href="{{ url('services/' . ($service->category ? $service->category . '/' . $service->seo_url : $service->seo_url)) }}" class="silka-regular">{{ $service->seo_title }}</a>
              </li>
            @empty
              <!-- No services available -->
            @endforelse
            <li>
              <a href="#" data-toggle="modal" data-target="#serviceTypeModal">Home Interior Price Calculator</a>
            </li>
          </ul>
        </div>
        <div class="col-lg-5 col-md-4 col-12">

        </div>
        <div class="col-lg-3 col-md-4 col-12">
          <h3 class="silka-regular">Contact Us</h3>
          @forelse($cities as $city)
            <p class="mt-25"> - <a href="{{ route('interior.cities.detail', $city->seo_url) }}" class="ng-binding" target="_blank">Interior Designers in {{ $city->name }}</a></p>
          @empty
          @endforelse

          <p class="mt-25">PHONE : <a href="tel:{{config('app.contact_no')}}" class="ng-binding">{{config('app.contact_no')}}</a></p>
          <p class="mt-25">EMAIL : <a href="mailto:{{config('mail.team_email')}}" class="ng-binding">{{config('mail.team_email')}}</a></p>
          <div class="social">
            <a href="#" target="_blank" class="social__item social__item--circle">
            <i class="fa fa-facebook" aria-hidden="true"></i>
            </a>
            <a href="#" target="_blank" class="social__item social__item--circle">
            <i class="fa fa-instagram" aria-hidden="true"></i>
            </a>
            <a href="#" target="_blank" class="social__item social__item--circle">
            <i class="fa fa-linkedin" aria-hidden="true"></i>
            </a>
            {{-- <a href="#" target="_blank" class="social__item social__item--circle">
            <i class="fa fa-youtube" aria-hidden="true"></i>
            </a> --}}
            <a href="#" target="_blank" class="social__item social__item--circle">
            <i class="fa fa-twitter" aria-hidden="true"></i>
            </a>
          </div>
        </div>
      </div>
      <br>
    </div>
    <section style="margin-bottom:45px">
      <div class="container"style="margin-top:20px; text-align: left">
      </div>
      <div class="container"style="margin-top:20px; text-align: left">
        <div class="row">
          <div style="margin-left:20px">
            <strong>Designs</strong>
          </div>
          <h5 class="silka-regular" style="text-transform: none;line-height: 1.6;margin-left:20px; margin-bottom : 50px; word-spacing:2px">
            @forelse($designServices as $service)
              <span class="silka-regular">
                @if($service->parent_id)
                  @php
                    $parentCategory = $service->parent && $service->parent->category ? $service->parent->category->name : '';
                    $serviceCategory = ($service->category ? $service->category->name : $parentCategory);
                  @endphp
                  <a href="{{ url('services/' . $serviceCategory . '/' . $service->seo_url) }}">{{ $service->title }}</a> |
                @else
                  <a href="{{ url('services/' . ($service->category ? $service->category->name . '/' . $service->seo_url : $service->seo_url)) }}">{{ $service->title }}</a> |
                @endif
              </span>
            @empty
            @endforelse

          </h5>
        </div>
      </div>
    </section>
  </div>
</footer>
<section class="copyright">
  <div class="container">
    <div class="row">
      <div class="col-md-7">
        <h4 class="silka-regular">
          <span class="set"><a class="cus_ft silka-regular" href="/privacy-policy">Privacy Policy</a></span>
          <span class="set"><a href="/terms-conditions">T & C</a></span>
          <span class="set"><a href="/reviews">Reviews</a></span>
          <span class="set"><a href="/about-us">About Us</a></span>
          {{-- <span class="set"><a href="">Sitemap</a></span> --}}
        </h4>
      </div>
      <div class="col-md-5 cus_ali">
        <p class="silka-regular cus_mb">Copyright © <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" data-src="" alt="{{config('app.name')}}" style="margin-top:-10px;"> {{date('Y')}} | All Rights Reserved</p>
      </div>
    </div>
  </div>
</section>
<a href="" id="btn-to-top">
<i class="fa fa-chevron-up"></i>
</a>
