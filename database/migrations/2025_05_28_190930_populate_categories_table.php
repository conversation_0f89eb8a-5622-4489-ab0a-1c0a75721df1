<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all unique categories from services and portfolios
        $serviceCategories = DB::table('services')
            ->whereNotNull('category')
            ->where('category', '!=', '')
            ->distinct()
            ->pluck('category');

        $portfolioTypes = DB::table('portfolios')
            ->whereNotNull('project_type')
            ->where('project_type', '!=', '')
            ->distinct()
            ->pluck('project_type');

        // Combine and get unique values
        $allCategories = $serviceCategories->merge($portfolioTypes)->unique();

        // Define display names mapping
        $displayNames = [
            'kitchen' => 'Kitchen',
            'living' => 'Living Room',
            'rooms' => 'Bedroom',
            'office' => 'Office',
            'commercial' => 'Commercial',
            'in-progress' => 'In Progress',
        ];

        // Insert categories
        foreach ($allCategories as $category) {
            if (!empty($category)) {
                DB::table('categories')->insert([
                    'name' => $category,
                    'display_name' => $displayNames[$category] ?? ucfirst(str_replace('-', ' ', $category)),
                    'status' => 'active',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear all categories
        DB::table('categories')->truncate();
    }
};
