<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * WARNING: This migration removes the old category and project_type columns.
     * Only run this after confirming that the new normalized structure is working correctly.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('category');
        });

        Schema::table('portfolios', function (Blueprint $table) {
            $table->dropColumn('project_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->string('category', 50)->nullable()->after('title');
        });

        Schema::table('portfolios', function (Blueprint $table) {
            $table->enum('project_type', ['rooms', 'kitchen', 'living', 'in-progress', 'office', 'commercial'])->after('description');
        });
    }
};
