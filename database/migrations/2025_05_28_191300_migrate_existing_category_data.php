<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update services table to use category_id
        $services = DB::table('services')->whereNotNull('category')->where('category', '!=', '')->get();
        
        foreach ($services as $service) {
            $category = DB::table('categories')->where('name', $service->category)->first();
            if ($category) {
                DB::table('services')
                    ->where('id', $service->id)
                    ->update(['category_id' => $category->id]);
            }
        }

        // Update portfolios table to use category_id
        $portfolios = DB::table('portfolios')->whereNotNull('project_type')->where('project_type', '!=', '')->get();
        
        foreach ($portfolios as $portfolio) {
            $category = DB::table('categories')->where('name', $portfolio->project_type)->first();
            if ($category) {
                DB::table('portfolios')
                    ->where('id', $portfolio->id)
                    ->update(['category_id' => $category->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear category_id values
        DB::table('services')->update(['category_id' => null]);
        DB::table('portfolios')->update(['category_id' => null]);
    }
};
